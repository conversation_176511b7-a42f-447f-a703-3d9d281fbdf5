"""
Alumnium AI Framework Integration for Mobile App Automation

This module provides integration between the Alumnium AI framework and the existing
mobile automation platform, enabling AI-powered test healing and intelligent automation.
"""

import logging
import time
import json
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

# Alumnium imports with fallback handling
try:
    from alumnium import Alumni
    from alumnium.drivers import AppiumDriver as AlumniumAppiumDriver
    from alumnium.accessibility import UIAutomator2AccessibiltyTree, XCUITestAccessibilityTree
    from alumnium.models import Model
    ALUMNIUM_AVAILABLE = True
except ImportError:
    ALUMNIUM_AVAILABLE = False
    Alumni = None
    AlumniumAppiumDriver = None
    UIAutomator2AccessibiltyTree = None
    XCUITestAccessibilityTree = None
    Model = None

logger = logging.getLogger(__name__)


@dataclass
class AlumniumConfig:
    """Configuration for Alumnium AI integration"""
    enabled: bool = True
    model_provider: str = "openai"  # openai, anthropic, huggingface
    model_name: str = "gpt-3.5-turbo"
    api_key: Optional[str] = None
    healing_enabled: bool = True
    intelligent_waits: bool = True
    action_suggestions: bool = True
    fallback_to_original: bool = True
    max_healing_attempts: int = 3
    healing_confidence_threshold: float = 0.7


class AlumniumMobileAgent:
    """
    AI-powered mobile test agent using Alumnium framework
    Provides intelligent test healing, element finding, and action execution
    """
    
    def __init__(self, device_controller, config: Optional[AlumniumConfig] = None):
        """
        Initialize the Alumnium mobile agent
        
        Args:
            device_controller: The existing device controller instance
            config: Alumnium configuration settings
        """
        self.device_controller = device_controller
        self.config = config or AlumniumConfig()
        self.alumni_instance = None
        self.healing_cache = {}
        self.performance_metrics = {
            'healing_attempts': 0,
            'healing_successes': 0,
            'ai_suggestions_used': 0,
            'fallback_to_original': 0
        }
        
        # Initialize Alumnium if available and enabled
        if ALUMNIUM_AVAILABLE and self.config.enabled:
            self._initialize_alumnium()
        else:
            logger.warning("Alumnium not available or disabled, using fallback mode")
    
    def _initialize_alumnium(self):
        """Initialize the Alumnium AI framework"""
        try:
            # Configure the AI model
            if self.config.api_key:
                import os
                os.environ["OPENAI_API_KEY"] = self.config.api_key
            
            # Create Alumnium instance with the device controller's driver
            if hasattr(self.device_controller, 'driver') and self.device_controller.driver:
                # Configure model if specified
                model = None
                if self.config.model_provider and self.config.model_name:
                    model = Model(
                        provider=self.config.model_provider,
                        name=self.config.model_name
                    )
                
                self.alumni_instance = Alumni(
                    driver=self.device_controller.driver,
                    model=model
                )
                logger.info("Alumnium AI agent initialized successfully")
            else:
                logger.error("Device controller driver not available for Alumnium initialization")
                
        except Exception as e:
            logger.error(f"Failed to initialize Alumnium: {e}")
            self.alumni_instance = None
    
    def is_available(self) -> bool:
        """Check if Alumnium AI agent is available and ready"""
        return self.alumni_instance is not None
    
    def heal_element_locator(self, original_locator: str, locator_type: str, 
                           context: str = None) -> Optional[Dict[str, Any]]:
        """
        Use AI to heal a failed element locator
        
        Args:
            original_locator: The original locator that failed
            locator_type: Type of locator (xpath, id, accessibility_id, etc.)
            context: Context about what the element should do
            
        Returns:
            Dictionary with healed locator information or None if healing failed
        """
        if not self.is_available() or not self.config.healing_enabled:
            return None
        
        self.performance_metrics['healing_attempts'] += 1
        
        try:
            # Check healing cache first
            cache_key = f"{locator_type}:{original_locator}"
            if cache_key in self.healing_cache:
                cached_result = self.healing_cache[cache_key]
                if time.time() - cached_result['timestamp'] < 300:  # 5 minutes cache
                    logger.debug(f"Using cached healing result for {cache_key}")
                    return cached_result['result']
            
            # Use Alumnium to analyze the current screen and suggest alternatives
            healing_prompt = self._create_healing_prompt(original_locator, locator_type, context)
            
            # Get AI suggestions for element location
            suggestions = self.alumni_instance.get(healing_prompt)
            
            if suggestions and isinstance(suggestions, (list, str)):
                healed_locators = self._parse_healing_suggestions(suggestions, locator_type)
                
                if healed_locators:
                    result = {
                        'original_locator': original_locator,
                        'healed_locators': healed_locators,
                        'confidence': self._calculate_healing_confidence(healed_locators),
                        'healing_method': 'ai_analysis'
                    }
                    
                    # Cache the result
                    self.healing_cache[cache_key] = {
                        'result': result,
                        'timestamp': time.time()
                    }
                    
                    self.performance_metrics['healing_successes'] += 1
                    logger.info(f"Successfully healed locator: {original_locator}")
                    return result
            
        except Exception as e:
            logger.error(f"Error during AI healing: {e}")
        
        return None
    
    def suggest_intelligent_wait(self, element_description: str, 
                               timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        Use AI to suggest intelligent wait strategies
        
        Args:
            element_description: Description of what to wait for
            timeout: Maximum timeout in seconds
            
        Returns:
            Dictionary with wait strategy suggestions
        """
        if not self.is_available() or not self.config.intelligent_waits:
            return None
        
        try:
            wait_prompt = f"""
            Analyze the current mobile app screen and suggest the best wait strategy for: {element_description}
            
            Consider:
            1. Network loading indicators
            2. Animation states
            3. Element visibility patterns
            4. App-specific loading behaviors
            
            Provide specific wait conditions and estimated timeouts.
            """
            
            suggestions = self.alumni_instance.get(wait_prompt)
            
            if suggestions:
                return {
                    'strategy': 'ai_intelligent_wait',
                    'suggestions': suggestions,
                    'max_timeout': timeout,
                    'confidence': 0.8
                }
                
        except Exception as e:
            logger.error(f"Error getting intelligent wait suggestions: {e}")
        
        return None
    
    def suggest_actions(self, goal: str) -> Optional[List[Dict[str, Any]]]:
        """
        Use AI to suggest actions to achieve a goal
        
        Args:
            goal: Natural language description of what to achieve
            
        Returns:
            List of suggested actions
        """
        if not self.is_available() or not self.config.action_suggestions:
            return None
        
        try:
            self.performance_metrics['ai_suggestions_used'] += 1
            
            # Use Alumnium's do method to get action suggestions
            action_plan = self.alumni_instance.do(goal)
            
            if action_plan:
                return self._convert_alumnium_actions(action_plan)
                
        except Exception as e:
            logger.error(f"Error getting action suggestions: {e}")
        
        return None
    
    def verify_with_ai(self, assertion: str) -> Optional[bool]:
        """
        Use AI to verify an assertion about the current screen
        
        Args:
            assertion: Natural language assertion to verify
            
        Returns:
            True if assertion is verified, False if not, None if AI unavailable
        """
        if not self.is_available():
            return None
        
        try:
            result = self.alumni_instance.check(assertion, vision=True)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Error during AI verification: {e}")
            return None
    
    def _create_healing_prompt(self, locator: str, locator_type: str, context: str) -> str:
        """Create a prompt for AI-powered locator healing"""
        prompt = f"""
        The mobile app element locator '{locator}' of type '{locator_type}' has failed.
        
        Context: {context or 'No specific context provided'}
        
        Analyze the current screen and suggest alternative locators that could find the same element.
        Consider:
        1. Similar elements with different attributes
        2. Parent/child element relationships
        3. Text content or accessibility labels
        4. Position-based alternatives
        
        Provide 3-5 alternative locators in order of confidence.
        """
        return prompt
    
    def _parse_healing_suggestions(self, suggestions: Any, locator_type: str) -> List[Dict[str, Any]]:
        """Parse AI suggestions into structured locator alternatives"""
        alternatives = []

        try:
            if isinstance(suggestions, str):
                # Parse string suggestions - look for common patterns
                lines = suggestions.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    # Try to extract locator patterns
                    alternative = self._extract_locator_from_text(line, locator_type)
                    if alternative:
                        alternatives.append(alternative)

            elif isinstance(suggestions, list):
                for suggestion in suggestions:
                    if isinstance(suggestion, dict):
                        alternatives.append(suggestion)
                    elif isinstance(suggestion, str):
                        alternative = self._extract_locator_from_text(suggestion, locator_type)
                        if alternative:
                            alternatives.append(alternative)

            elif isinstance(suggestions, dict):
                # Single suggestion in dict format
                alternatives.append(suggestions)

        except Exception as e:
            logger.error(f"Error parsing healing suggestions: {e}")

        return alternatives[:5]  # Limit to top 5 suggestions

    def _extract_locator_from_text(self, text: str, original_type: str) -> Optional[Dict[str, Any]]:
        """Extract locator information from text suggestion"""
        try:
            # Common patterns for different locator types
            patterns = {
                'xpath': [r'xpath[:\s]*["\']([^"\']+)["\']', r'//[^"\s]+'],
                'id': [r'id[:\s]*["\']([^"\']+)["\']', r'@id[:\s]*["\']([^"\']+)["\']'],
                'accessibility_id': [r'accessibility[_\s]*id[:\s]*["\']([^"\']+)["\']'],
                'class_name': [r'class[_\s]*name[:\s]*["\']([^"\']+)["\']'],
                'text': [r'text[:\s]*["\']([^"\']+)["\']']
            }

            import re

            # Try to find locator patterns in the text
            for locator_type, type_patterns in patterns.items():
                for pattern in type_patterns:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        value = match.group(1) if match.groups() else match.group(0)
                        confidence = 0.8 if locator_type == original_type else 0.6

                        return {
                            'type': locator_type,
                            'value': value,
                            'confidence': confidence,
                            'source': 'ai_text_extraction'
                        }

            # Fallback: if no pattern matches, try to use the text as-is for certain types
            if original_type in ['text', 'accessibility_id']:
                # Clean the text and use it directly
                clean_text = text.strip('"\'').strip()
                if clean_text and len(clean_text) > 2:
                    return {
                        'type': original_type,
                        'value': clean_text,
                        'confidence': 0.5,
                        'source': 'ai_direct_text'
                    }

        except Exception as e:
            logger.debug(f"Error extracting locator from text: {e}")

        return None
    
    def _calculate_healing_confidence(self, healed_locators: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for healed locators"""
        if not healed_locators:
            return 0.0
        
        # Simple confidence calculation based on number of alternatives
        base_confidence = min(0.9, len(healed_locators) * 0.2 + 0.5)
        return base_confidence
    
    def _convert_alumnium_actions(self, action_plan: Any) -> List[Dict[str, Any]]:
        """Convert Alumnium action plan to framework-compatible format"""
        actions = []

        try:
            if isinstance(action_plan, str):
                # Parse string action plan
                actions = self._parse_action_plan_text(action_plan)
            elif isinstance(action_plan, list):
                for action in action_plan:
                    converted = self._convert_single_action(action)
                    if converted:
                        actions.append(converted)
            elif isinstance(action_plan, dict):
                converted = self._convert_single_action(action_plan)
                if converted:
                    actions.append(converted)

        except Exception as e:
            logger.error(f"Error converting Alumnium actions: {e}")

        return actions

    def _parse_action_plan_text(self, text: str) -> List[Dict[str, Any]]:
        """Parse text-based action plan into structured actions"""
        actions = []
        lines = text.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            # Try to identify action types
            action = None
            if 'click' in line.lower() or 'tap' in line.lower():
                action = {'type': 'tap', 'description': line}
            elif 'type' in line.lower() or 'input' in line.lower():
                action = {'type': 'input_text', 'description': line}
            elif 'swipe' in line.lower() or 'scroll' in line.lower():
                action = {'type': 'swipe', 'description': line}
            elif 'wait' in line.lower():
                action = {'type': 'wait', 'description': line}

            if action:
                action['confidence'] = 0.7
                action['source'] = 'ai_text_parsing'
                actions.append(action)

        return actions

    def _convert_single_action(self, action: Any) -> Optional[Dict[str, Any]]:
        """Convert a single action to framework format"""
        if isinstance(action, dict):
            return {
                'type': action.get('type', 'unknown'),
                'parameters': action.get('parameters', {}),
                'confidence': action.get('confidence', 0.7),
                'source': 'ai_structured'
            }
        elif isinstance(action, str):
            return {
                'type': 'custom',
                'description': action,
                'confidence': 0.6,
                'source': 'ai_text'
            }

        return None

    def create_intelligent_wait_strategy(self, element_description: str,
                                       context: str = None) -> Dict[str, Any]:
        """
        Create an intelligent wait strategy based on element description and context

        Args:
            element_description: Description of the element to wait for
            context: Context of the wait operation

        Returns:
            Dictionary with wait strategy details
        """
        strategy = {
            'type': 'intelligent_wait',
            'element_description': element_description,
            'context': context,
            'conditions': [],
            'timeout': 30,
            'poll_frequency': 0.5
        }

        # Analyze element description for wait conditions
        desc_lower = element_description.lower()

        # Loading indicators
        if any(word in desc_lower for word in ['loading', 'spinner', 'progress']):
            strategy['conditions'].append({
                'type': 'wait_for_invisibility',
                'description': 'Wait for loading indicator to disappear',
                'timeout': 60
            })

        # Buttons and interactive elements
        elif any(word in desc_lower for word in ['button', 'click', 'tap']):
            strategy['conditions'].append({
                'type': 'wait_for_clickable',
                'description': 'Wait for element to be clickable',
                'timeout': 20
            })

        # Text fields
        elif any(word in desc_lower for word in ['input', 'field', 'text']):
            strategy['conditions'].append({
                'type': 'wait_for_presence',
                'description': 'Wait for input field to be present',
                'timeout': 15
            })

        # Lists and content
        elif any(word in desc_lower for word in ['list', 'content', 'data']):
            strategy['conditions'].append({
                'type': 'wait_for_presence',
                'description': 'Wait for content to load',
                'timeout': 30
            })
            strategy['conditions'].append({
                'type': 'wait_for_stable',
                'description': 'Wait for content to stabilize',
                'timeout': 10
            })

        # Default condition
        if not strategy['conditions']:
            strategy['conditions'].append({
                'type': 'wait_for_presence',
                'description': 'Wait for element to be present',
                'timeout': 20
            })

        return strategy

    def implement_failure_recovery(self, failure_type: str,
                                 failure_context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Implement intelligent failure recovery strategies

        Args:
            failure_type: Type of failure (element_not_found, timeout, etc.)
            failure_context: Context information about the failure

        Returns:
            Recovery strategy or None if no strategy available
        """
        recovery_strategy = None

        try:
            if failure_type == 'element_not_found':
                recovery_strategy = self._handle_element_not_found_recovery(failure_context)
            elif failure_type == 'timeout':
                recovery_strategy = self._handle_timeout_recovery(failure_context)
            elif failure_type == 'stale_element':
                recovery_strategy = self._handle_stale_element_recovery(failure_context)
            elif failure_type == 'session_lost':
                recovery_strategy = self._handle_session_lost_recovery(failure_context)

            if recovery_strategy:
                logger.info(f"AI recovery strategy created for {failure_type}: {recovery_strategy['description']}")

        except Exception as e:
            logger.error(f"Error creating recovery strategy: {e}")

        return recovery_strategy

    def _handle_element_not_found_recovery(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle element not found recovery"""
        return {
            'type': 'element_recovery',
            'description': 'AI-powered element healing and alternative locator search',
            'steps': [
                {'action': 'ai_heal_locator', 'params': context},
                {'action': 'try_alternative_locators', 'params': context},
                {'action': 'wait_and_retry', 'params': {'timeout': 10}},
                {'action': 'take_screenshot_for_analysis', 'params': {}}
            ],
            'max_attempts': 3,
            'confidence': 0.8
        }

    def _handle_timeout_recovery(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle timeout recovery"""
        return {
            'type': 'timeout_recovery',
            'description': 'Intelligent timeout handling with adaptive wait strategies',
            'steps': [
                {'action': 'analyze_page_state', 'params': {}},
                {'action': 'check_for_loading_indicators', 'params': {}},
                {'action': 'extend_timeout_intelligently', 'params': context},
                {'action': 'try_alternative_wait_conditions', 'params': context}
            ],
            'max_attempts': 2,
            'confidence': 0.7
        }

    def _handle_stale_element_recovery(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle stale element recovery"""
        return {
            'type': 'stale_element_recovery',
            'description': 'Re-find element using original locator or AI alternatives',
            'steps': [
                {'action': 'refind_element', 'params': context},
                {'action': 'ai_heal_if_refind_fails', 'params': context},
                {'action': 'verify_element_stability', 'params': {}}
            ],
            'max_attempts': 2,
            'confidence': 0.9
        }

    def _handle_session_lost_recovery(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle session lost recovery"""
        return {
            'type': 'session_recovery',
            'description': 'Session restoration with state preservation',
            'steps': [
                {'action': 'detect_session_state', 'params': {}},
                {'action': 'attempt_session_restoration', 'params': context},
                {'action': 'verify_app_state', 'params': {}},
                {'action': 'navigate_to_previous_state', 'params': context}
            ],
            'max_attempts': 1,
            'confidence': 0.6
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the AI agent"""
        metrics = self.performance_metrics.copy()
        if metrics['healing_attempts'] > 0:
            metrics['healing_success_rate'] = metrics['healing_successes'] / metrics['healing_attempts']
        else:
            metrics['healing_success_rate'] = 0.0
        
        return metrics
    
    def cleanup(self):
        """Cleanup resources"""
        if self.alumni_instance:
            try:
                self.alumni_instance.quit()
            except Exception as e:
                logger.error(f"Error during Alumnium cleanup: {e}")


# Global instance for easy access
alumnium_agent = None


def initialize_alumnium_agent(device_controller, config: Optional[AlumniumConfig] = None):
    """Initialize the global Alumnium agent instance"""
    global alumnium_agent
    alumnium_agent = AlumniumMobileAgent(device_controller, config)
    return alumnium_agent


def get_alumnium_agent() -> Optional[AlumniumMobileAgent]:
    """Get the global Alumnium agent instance"""
    return alumnium_agent


def create_default_config(use_open_source: bool = True) -> AlumniumConfig:
    """
    Create a default Alumnium configuration

    Args:
        use_open_source: If True, configure for open-source models (Hugging Face)
                        If False, use OpenAI/Anthropic models

    Returns:
        AlumniumConfig with appropriate settings
    """
    if use_open_source:
        return AlumniumConfig(
            enabled=True,
            model_provider="huggingface",
            model_name="microsoft/DialoGPT-medium",  # Free open-source model
            api_key=None,  # No API key needed for local models
            healing_enabled=True,
            intelligent_waits=True,
            action_suggestions=True,
            fallback_to_original=True,
            max_healing_attempts=3,
            healing_confidence_threshold=0.6  # Lower threshold for open-source models
        )
    else:
        return AlumniumConfig(
            enabled=True,
            model_provider="openai",
            model_name="gpt-3.5-turbo",
            api_key=None,  # Should be set via environment variable
            healing_enabled=True,
            intelligent_waits=True,
            action_suggestions=True,
            fallback_to_original=True,
            max_healing_attempts=3,
            healing_confidence_threshold=0.7
        )
