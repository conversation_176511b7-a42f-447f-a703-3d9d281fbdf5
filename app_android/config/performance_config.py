# Performance optimization configuration for Android test execution
# Implements recommendations from ANDROID_PERFORMANCE_RECOMMENDATIONS.md

import time
import threading
from typing import Dict, Any, Optional

# Import global_values_db to load disable_screenshots setting
try:
    from app.utils.global_values_db import global_values_db
    _GLOBAL_VALUES_AVAILABLE = True
except ImportError:
    _GLOBAL_VALUES_AVAILABLE = False

class PerformanceConfig:
    """
    Configuration class for Android test execution performance optimizations
    """
    
    def __init__(self):
        # Global screenshot disable setting - load from database
        self.disable_screenshots = self._load_disable_screenshots_setting()
        
        # Screenshot optimization settings (Step 1) - CRITICAL PERFORMANCE OPTIMIZATION
        # Based on analysis: 60-75% of execution time spent on screenshots
        self.screenshot_cache_enabled = True
        self.screenshot_cache_ttl = 5.0  # Increased cache time for better performance
        self.screenshot_frequency_limit = 5.0  # CRITICAL: Increased from 2.5s to 5s (70% reduction)
        self.screenshot_skip_duplicates = True
        self.screenshot_compression_enabled = True
        self.screenshot_compression_quality = 70  # Further reduced for speed
        self.screenshot_on_element_failure_only = True  # Only take screenshots on failures
        self.screenshot_skip_during_element_search = True  # Skip during element finding
        self.screenshot_skip_during_health_checks = True  # NEW: Skip during health checks
        self.screenshot_intelligent_context = True  # NEW: Context-aware screenshot decisions
        self.screenshot_max_per_minute = 12  # NEW: Hard limit of 12 screenshots per minute
        
        # Health check optimization settings (Step 2) - MAJOR PERFORMANCE IMPROVEMENT
        # Based on analysis: 56 suspend/resume cycles causing significant overhead
        self.health_check_interval = 30.0  # CRITICAL: Increased from 15s to 30s (50% reduction)
        self.health_check_timeout = 3.0  # Reduced timeout for faster failures
        self.session_refresh_threshold = 3  # Reduce to 3 failures for faster recovery
        self.session_validation_frequency = 60.0  # Increased from 45s to 60s
        self.suspend_health_checks_during_actions = True
        self.element_search_health_check_suspension = True  # Suspend during element searches
        self.health_check_smart_suspension = True  # NEW: Intelligent suspension logic
        self.health_check_batch_operations = True  # NEW: Batch multiple checks
        self.health_check_async_mode = True  # NEW: Non-blocking health checks
        
        # Performance monitoring
        self.enable_performance_metrics = True
        self.log_performance_warnings = True
        self.performance_warning_threshold = 2.0  # Warn if operations take > 2s
        
        # Cache and state management
        self._screenshot_cache = {}
        self._last_screenshot_time = 0
        self._last_health_check_time = 0
        self._cache_lock = threading.Lock()

        # NEW: Performance tracking for optimization validation
        self._screenshot_count_per_minute = []
        self._health_check_suspension_count = 0
        self._performance_metrics = {
            'screenshots_skipped': 0,
            'health_checks_optimized': 0,
            'total_time_saved': 0.0
        }
    
    def _load_disable_screenshots_setting(self) -> bool:
        """
        Load the disable_screenshots setting from the global values database
        
        Returns:
            bool: True if screenshots should be disabled, False otherwise
        """
        if not _GLOBAL_VALUES_AVAILABLE:
            return False  # Default to enabled if database is not available
            
        try:
            disabled = global_values_db.get_value('disable_screenshots')
            if disabled is True or str(disabled).lower() == 'true':
                return True
            return False
        except Exception:
             return False  # Default to enabled if there's any error
    
    def reload_disable_screenshots_setting(self):
        """
        Reload the disable_screenshots setting from the database
        This allows dynamic updates without restarting the application
        """
        self.disable_screenshots = self._load_disable_screenshots_setting()
        
    def should_take_screenshot(self, action_id: str = None, context: str = None) -> bool:
        """
        Determine if a screenshot should be taken based on frequency limits and context

        Args:
            action_id: Optional action ID to check for duplicates
            context: Context of the screenshot (e.g., 'element_search', 'action_execution', 'failure')

        Returns:
            bool: True if screenshot should be taken
        """
        # Global disable check - reload setting to pick up dynamic changes
        self.reload_disable_screenshots_setting()
        if self.disable_screenshots:
            return False
            
        current_time = time.time()

        # NEW: Enhanced context-aware screenshot decisions
        if self.screenshot_intelligent_context:
            # Skip screenshots during element search if configured
            if context == 'element_search' and self.screenshot_skip_during_element_search:
                self._performance_metrics['screenshots_skipped'] += 1
                return False

            # Skip screenshots during health checks
            if context == 'health_check' and self.screenshot_skip_during_health_checks:
                self._performance_metrics['screenshots_skipped'] += 1
                return False

            # Skip screenshots for routine operations
            if context in ['session_validation', 'connection_check', 'heartbeat']:
                self._performance_metrics['screenshots_skipped'] += 1
                return False

        # Always take screenshots on failures (unless globally disabled)
        if context == 'failure':
            return True

        # NEW: Check per-minute limit (hard cap for performance)
        if self.screenshot_max_per_minute and self._check_per_minute_limit():
            self._performance_metrics['screenshots_skipped'] += 1
            return False

        # Check frequency limit (CRITICAL OPTIMIZATION)
        if current_time - self._last_screenshot_time < self.screenshot_frequency_limit:
            self._performance_metrics['screenshots_skipped'] += 1
            return False

        # Check for duplicate action_id in cache
        if action_id and self.screenshot_skip_duplicates:
            with self._cache_lock:
                if action_id in self._screenshot_cache:
                    cache_entry = self._screenshot_cache[action_id]
                    if current_time - cache_entry['timestamp'] < self.screenshot_cache_ttl:
                        return False

        return True
    
    def should_perform_health_check(self) -> bool:
        """
        Determine if a health check should be performed based on interval
        
        Returns:
            bool: True if health check should be performed
        """
        current_time = time.time()
        return current_time - self._last_health_check_time >= self.health_check_interval
    
    def update_screenshot_cache(self, action_id: str, screenshot_path: str):
        """
        Update the screenshot cache with new entry
        
        Args:
            action_id: Action ID for the screenshot
            screenshot_path: Path to the screenshot file
        """
        if not action_id or not self.screenshot_cache_enabled:
            return
            
        current_time = time.time()
        with self._cache_lock:
            self._screenshot_cache[action_id] = {
                'path': screenshot_path,
                'timestamp': current_time
            }
            self._last_screenshot_time = current_time
            
            # Clean old cache entries
            self._clean_cache(current_time)
    
    def get_cached_screenshot(self, action_id: str) -> Optional[str]:
        """
        Get cached screenshot path if available and valid
        
        Args:
            action_id: Action ID to look up
            
        Returns:
            str or None: Screenshot path if cached and valid
        """
        if not action_id or not self.screenshot_cache_enabled:
            return None
            
        current_time = time.time()
        with self._cache_lock:
            if action_id in self._screenshot_cache:
                cache_entry = self._screenshot_cache[action_id]
                if current_time - cache_entry['timestamp'] < self.screenshot_cache_ttl:
                    return cache_entry['path']
                else:
                    # Remove expired entry
                    del self._screenshot_cache[action_id]
        
        return None
    
    def update_health_check_time(self):
        """
        Update the last health check time
        """
        self._last_health_check_time = time.time()
    
    def _clean_cache(self, current_time: float):
        """
        Clean expired entries from the screenshot cache
        
        Args:
            current_time: Current timestamp
        """
        expired_keys = []
        for key, entry in self._screenshot_cache.items():
            if current_time - entry['timestamp'] > self.screenshot_cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._screenshot_cache[key]
    
    def _check_per_minute_limit(self) -> bool:
        """
        Check if we've exceeded the per-minute screenshot limit

        Returns:
            bool: True if limit exceeded, False otherwise
        """
        current_time = time.time()

        # Clean old entries (older than 1 minute)
        self._screenshot_count_per_minute = [
            timestamp for timestamp in self._screenshot_count_per_minute
            if current_time - timestamp < 60
        ]

        # Check if we've hit the limit
        return len(self._screenshot_count_per_minute) >= self.screenshot_max_per_minute

    def record_screenshot_taken(self):
        """Record that a screenshot was taken for per-minute tracking"""
        current_time = time.time()
        self._screenshot_count_per_minute.append(current_time)
        self._last_screenshot_time = current_time

    def should_suspend_health_checks(self, operation_type: str) -> bool:
        """
        Determine if health checks should be suspended for an operation

        Args:
            operation_type: Type of operation being performed

        Returns:
            bool: True if health checks should be suspended
        """
        if not self.health_check_smart_suspension:
            return self.suspend_health_checks_during_actions

        # Smart suspension logic - only suspend for critical operations
        critical_operations = [
            'session_creation', 'app_launch', 'driver_initialization',
            'screenshot_capture', 'element_search', 'action_execution'
        ]

        should_suspend = operation_type in critical_operations
        if should_suspend:
            self._health_check_suspension_count += 1
            self._performance_metrics['health_checks_optimized'] += 1

        return should_suspend

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get current performance optimization metrics

        Returns:
            dict: Performance metrics
        """
        return {
            'screenshots_skipped': self._performance_metrics['screenshots_skipped'],
            'health_checks_optimized': self._performance_metrics['health_checks_optimized'],
            'health_check_suspensions': self._health_check_suspension_count,
            'total_time_saved_estimate': self._performance_metrics['screenshots_skipped'] * 0.8,  # 0.8s per screenshot
            'current_screenshot_frequency': self.screenshot_frequency_limit,
            'current_health_check_interval': self.health_check_interval
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get current performance statistics

        Returns:
            dict: Performance statistics
        """
        with self._cache_lock:
            return {
                'screenshot_cache_size': len(self._screenshot_cache),
                'last_screenshot_time': self._last_screenshot_time,
                'last_health_check_time': self._last_health_check_time,
                'screenshot_frequency_limit': self.screenshot_frequency_limit,
                'health_check_interval': self.health_check_interval,
                'cache_enabled': self.screenshot_cache_enabled,
                **self.get_performance_metrics()  # Include optimization metrics
            }

# Global performance configuration instance
performance_config = PerformanceConfig()