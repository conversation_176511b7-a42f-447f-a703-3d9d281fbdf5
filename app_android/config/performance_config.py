# Performance optimization configuration for Android test execution
# Implements recommendations from ANDROID_PERFORMANCE_RECOMMENDATIONS.md

import time
import threading
from typing import Dict, Any, Optional

# Import global_values_db to load disable_screenshots setting
try:
    from app.utils.global_values_db import global_values_db
    _GLOBAL_VALUES_AVAILABLE = True
except ImportError:
    _GLOBAL_VALUES_AVAILABLE = False

class PerformanceConfig:
    """
    Configuration class for Android test execution performance optimizations
    """
    
    def __init__(self):
        # Global screenshot disable setting - load from database
        self.disable_screenshots = self._load_disable_screenshots_setting()
        
        # Screenshot optimization settings (Step 1) - Enhanced for element identification
        self.screenshot_cache_enabled = True
        self.screenshot_cache_ttl = 3.0  # Cache screenshots for 3 seconds (increased)
        self.screenshot_frequency_limit = 2.5  # Minimum 2.5 seconds between screenshots (increased)
        self.screenshot_skip_duplicates = True
        self.screenshot_compression_enabled = True
        self.screenshot_compression_quality = 80  # Reduced quality for better performance
        self.screenshot_on_element_failure_only = True  # Only take screenshots on failures
        self.screenshot_skip_during_element_search = True  # Skip during element finding
        
        # Session management optimization settings (Step 3) - Enhanced for stability
        self.health_check_interval = 15.0  # Increase from 10s to 15s for better performance
        self.health_check_timeout = 5.0  # Increase timeout for more reliable checks
        self.session_refresh_threshold = 3  # Reduce to 3 failures for faster recovery
        self.session_validation_frequency = 45.0  # Check session every 45s for better performance
        self.suspend_health_checks_during_actions = True
        self.element_search_health_check_suspension = True  # Suspend during element searches
        
        # Performance monitoring
        self.enable_performance_metrics = True
        self.log_performance_warnings = True
        self.performance_warning_threshold = 2.0  # Warn if operations take > 2s
        
        # Cache and state management
        self._screenshot_cache = {}
        self._last_screenshot_time = 0
        self._last_health_check_time = 0
        self._cache_lock = threading.Lock()
    
    def _load_disable_screenshots_setting(self) -> bool:
        """
        Load the disable_screenshots setting from the global values database
        
        Returns:
            bool: True if screenshots should be disabled, False otherwise
        """
        if not _GLOBAL_VALUES_AVAILABLE:
            return False  # Default to enabled if database is not available
            
        try:
            disabled = global_values_db.get_value('disable_screenshots')
            if disabled is True or str(disabled).lower() == 'true':
                return True
            return False
        except Exception:
             return False  # Default to enabled if there's any error
    
    def reload_disable_screenshots_setting(self):
        """
        Reload the disable_screenshots setting from the database
        This allows dynamic updates without restarting the application
        """
        self.disable_screenshots = self._load_disable_screenshots_setting()
        
    def should_take_screenshot(self, action_id: str = None, context: str = None) -> bool:
        """
        Determine if a screenshot should be taken based on frequency limits and context

        Args:
            action_id: Optional action ID to check for duplicates
            context: Context of the screenshot (e.g., 'element_search', 'action_execution', 'failure')

        Returns:
            bool: True if screenshot should be taken
        """
        # Global disable check - reload setting to pick up dynamic changes
        self.reload_disable_screenshots_setting()
        if self.disable_screenshots:
            return False
            
        current_time = time.time()

        # Skip screenshots during element search if configured
        if context == 'element_search' and self.screenshot_skip_during_element_search:
            return False

        # Always take screenshots on failures (unless globally disabled)
        if context == 'failure':
            return True

        # Check frequency limit
        if current_time - self._last_screenshot_time < self.screenshot_frequency_limit:
            return False

        # Check for duplicate action_id in cache
        if action_id and self.screenshot_skip_duplicates:
            with self._cache_lock:
                if action_id in self._screenshot_cache:
                    cache_entry = self._screenshot_cache[action_id]
                    if current_time - cache_entry['timestamp'] < self.screenshot_cache_ttl:
                        return False

        return True
    
    def should_perform_health_check(self) -> bool:
        """
        Determine if a health check should be performed based on interval
        
        Returns:
            bool: True if health check should be performed
        """
        current_time = time.time()
        return current_time - self._last_health_check_time >= self.health_check_interval
    
    def update_screenshot_cache(self, action_id: str, screenshot_path: str):
        """
        Update the screenshot cache with new entry
        
        Args:
            action_id: Action ID for the screenshot
            screenshot_path: Path to the screenshot file
        """
        if not action_id or not self.screenshot_cache_enabled:
            return
            
        current_time = time.time()
        with self._cache_lock:
            self._screenshot_cache[action_id] = {
                'path': screenshot_path,
                'timestamp': current_time
            }
            self._last_screenshot_time = current_time
            
            # Clean old cache entries
            self._clean_cache(current_time)
    
    def get_cached_screenshot(self, action_id: str) -> Optional[str]:
        """
        Get cached screenshot path if available and valid
        
        Args:
            action_id: Action ID to look up
            
        Returns:
            str or None: Screenshot path if cached and valid
        """
        if not action_id or not self.screenshot_cache_enabled:
            return None
            
        current_time = time.time()
        with self._cache_lock:
            if action_id in self._screenshot_cache:
                cache_entry = self._screenshot_cache[action_id]
                if current_time - cache_entry['timestamp'] < self.screenshot_cache_ttl:
                    return cache_entry['path']
                else:
                    # Remove expired entry
                    del self._screenshot_cache[action_id]
        
        return None
    
    def update_health_check_time(self):
        """
        Update the last health check time
        """
        self._last_health_check_time = time.time()
    
    def _clean_cache(self, current_time: float):
        """
        Clean expired entries from the screenshot cache
        
        Args:
            current_time: Current timestamp
        """
        expired_keys = []
        for key, entry in self._screenshot_cache.items():
            if current_time - entry['timestamp'] > self.screenshot_cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._screenshot_cache[key]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get current performance statistics
        
        Returns:
            dict: Performance statistics
        """
        with self._cache_lock:
            return {
                'screenshot_cache_size': len(self._screenshot_cache),
                'last_screenshot_time': self._last_screenshot_time,
                'last_health_check_time': self._last_health_check_time,
                'screenshot_frequency_limit': self.screenshot_frequency_limit,
                'health_check_interval': self.health_check_interval,
                'cache_enabled': self.screenshot_cache_enabled
            }

# Global performance configuration instance
performance_config = PerformanceConfig()