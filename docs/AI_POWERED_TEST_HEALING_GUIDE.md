# AI-Powered Test Healing Guide

## Overview

This guide provides comprehensive documentation for the AI-powered test healing capabilities integrated into the mobile automation framework using the Alumnium AI framework. The system provides intelligent test recovery, self-healing locators, and automated failure resolution.

## Features

### 1. Self-Healing Locators
- **Automatic Locator Recovery**: When element locators fail, AI analyzes the current screen and suggests alternative locators
- **Confidence-Based Selection**: AI provides confidence scores for each suggested locator
- **Fallback Mechanisms**: Graceful degradation to original methods when AI is unavailable

### 2. Intelligent Wait Strategies
- **Context-Aware Waits**: AI analyzes element descriptions to create optimal wait conditions
- **Dynamic Timeout Adjustment**: Intelligent timeout calculation based on element type and context
- **Loading State Detection**: Automatic detection of loading indicators and wait conditions

### 3. Automatic Failure Recovery
- **Multi-Type Recovery**: Handles element not found, timeout, session lost, and stale element failures
- **Recovery Strategy Generation**: AI creates step-by-step recovery plans
- **Adaptive Retry Logic**: Intelligent retry mechanisms with learning capabilities

## Configuration

### Together AI Configuration (Recommended - Free Models)

The system is configured by default to use free models from Together AI. Get your free API key at https://api.together.xyz/:

```python
# Default configuration in alumnium_integration.py
alumnium_config = AlumniumConfig(
    enabled=True,
    model_provider="together",
    model_name="meta-llama/Llama-2-7b-chat-hf",  # Free Together AI model
    api_key=None,  # Will be loaded from TOGETHER_API_KEY env var
    base_url="https://api.together.xyz/v1",
    healing_enabled=True,
    intelligent_waits=True,
    action_suggestions=True,
    fallback_to_original=True,
    max_healing_attempts=3,
    healing_confidence_threshold=0.6
)
```

#### Setting up Together AI:
1. Get your free API key from https://api.together.xyz/
2. Add to your `.env` file:
```bash
TOGETHER_API_KEY=your_api_key_here
```

### Available Together AI Models (All Free)

#### Llama 2 Models
```python
# Llama 2 7B (Recommended - Good balance of speed and accuracy)
alumnium_config = AlumniumConfig(
    model_provider="together",
    model_name="meta-llama/Llama-2-7b-chat-hf"
)

# Llama 2 13B (More accurate, slower)
alumnium_config = AlumniumConfig(
    model_provider="together",
    model_name="meta-llama/Llama-2-13b-chat-hf"
)
```

#### Mistral Models
```python
# Mistral 7B (Fast and efficient)
alumnium_config = AlumniumConfig(
    model_provider="together",
    model_name="mistralai/Mistral-7B-Instruct-v0.1"
)
```

#### RedPajama Models
```python
# RedPajama 7B (Lightweight option)
alumnium_config = AlumniumConfig(
    model_provider="together",
    model_name="togethercomputer/RedPajama-INCITE-7B-Chat"
)
```

### Alternative Model Providers

#### OpenAI Configuration
```python
alumnium_config = AlumniumConfig(
    enabled=True,
    model_provider="openai",
    model_name="gpt-3.5-turbo",
    api_key=None,  # Set OPENAI_API_KEY in .env
    healing_confidence_threshold=0.7
)
```

#### Anthropic Configuration
```python
alumnium_config = AlumniumConfig(
    enabled=True,
    model_provider="anthropic",
    model_name="claude-3-sonnet-20240229",
    api_key=None,  # Set ANTHROPIC_API_KEY in .env
    healing_confidence_threshold=0.7
)
```

### Local Model Setup (Advanced)

For completely offline operation, you can set up local models:

```python
# Install required packages
# pip install transformers torch

alumnium_config = AlumniumConfig(
    enabled=True,
    model_provider="huggingface",
    model_name="microsoft/DialoGPT-medium",  # Or any local model
    api_key=None,
    healing_enabled=True
)
```

## Usage Examples

### 1. Automatic Integration

The AI healing is automatically integrated into the element finding process:

```python
# Standard element finding with automatic AI healing
element = controller.find_element_intelligently(
    locator_type="id",
    locator_value="submit_button",
    timeout=30,
    context="action_execution"
)
# If the locator fails, AI healing is automatically attempted
```

### 2. Manual AI Healing Action

You can also trigger AI healing manually:

```python
# Manual AI healing for a failed action
healing_params = {
    'healing_type': 'locator_healing',
    'failure_context': {
        'locator_type': 'id',
        'locator_value': 'submit_button',
        'error_message': 'Element not found',
        'context': 'button_click'
    },
    'original_action': {
        'action_type': 'tap',
        'target': 'submit_button'
    },
    'max_attempts': 3
}

from app_android.actions.ai_healing_action import AIHealingAction
healing_action = AIHealingAction(controller)
result = healing_action.execute(healing_params)
```

### 3. Intelligent Wait Strategies

```python
# Get AI-powered wait strategy
alumnium_agent = get_alumnium_agent()
wait_strategy = alumnium_agent.create_intelligent_wait_strategy(
    element_description="loading spinner for data refresh",
    context="data_loading"
)

# Apply the wait strategy
for condition in wait_strategy['conditions']:
    if condition['type'] == 'wait_for_invisibility':
        # Wait for loading spinner to disappear
        WebDriverWait(driver, condition['timeout']).until(
            EC.invisibility_of_element_located((By.ID, "loading_spinner"))
        )
```

### 4. Failure Recovery

```python
# Implement AI-powered failure recovery
recovery_strategy = alumnium_agent.implement_failure_recovery(
    failure_type='element_not_found',
    failure_context={
        'locator_type': 'xpath',
        'locator_value': '//button[@text="Submit"]',
        'action_attempted': 'tap',
        'screen_context': 'form_submission'
    }
)

# Execute recovery steps
for step in recovery_strategy['steps']:
    if step['action'] == 'ai_heal_locator':
        # Perform AI locator healing
        healing_result = alumnium_agent.heal_element_locator(...)
```

## Best Practices

### 1. Locator Design for AI Healing

**Good Locators for AI Healing:**
```python
# Descriptive IDs
locator = "id:submit_form_button"

# Meaningful accessibility IDs
locator = "accessibility_id:Submit Form"

# Descriptive XPath with text
locator = "xpath://button[contains(@text, 'Submit')]"
```

**Avoid:**
```python
# Generic or auto-generated IDs
locator = "id:btn_001"

# Complex XPath without context
locator = "xpath://div[3]/span[2]/button[1]"
```

### 2. Context Information

Provide rich context for better AI healing:

```python
# Good context
context_info = {
    'locator_type': 'id',
    'locator_value': 'login_button',
    'action_type': 'tap',
    'screen_context': 'login_form',
    'element_description': 'blue login button at bottom of form',
    'expected_behavior': 'navigate to dashboard after successful login'
}

# Use context in healing
healing_result = alumnium_agent.heal_element_locator(
    original_locator='login_button',
    locator_type='id',
    context='login form submission button for user authentication'
)
```

### 3. Performance Optimization

```python
# Configure AI healing for optimal performance
alumnium_config = AlumniumConfig(
    enabled=True,
    healing_enabled=True,
    max_healing_attempts=2,  # Limit attempts for speed
    healing_confidence_threshold=0.7,  # Higher threshold for accuracy
    fallback_to_original=True  # Always have fallback
)
```

### 4. Error Handling

```python
try:
    # Attempt action with AI healing
    element = controller.find_element_intelligently(
        locator_type="id",
        locator_value="target_element",
        context="critical_action"
    )
    element.click()
    
except Exception as e:
    # Manual healing as fallback
    healing_action = AIHealingAction(controller)
    healing_result = healing_action.execute({
        'healing_type': 'auto_detect',
        'failure_context': {
            'error_message': str(e),
            'locator_type': 'id',
            'locator_value': 'target_element'
        }
    })
    
    if healing_result['status'] == 'success':
        # Retry with healed locator
        healed_locator = healing_result['healing_result']['healed_locator']
        element = controller.find_element_intelligently(
            locator_type=healed_locator['type'],
            locator_value=healed_locator['value']
        )
        element.click()
    else:
        # Final fallback to manual intervention
        raise Exception(f"AI healing failed: {healing_result['message']}")
```

## Monitoring and Metrics

### Performance Metrics

Monitor AI healing performance:

```python
# Get AI agent metrics
alumnium_agent = get_alumnium_agent()
metrics = alumnium_agent.get_performance_metrics()

print(f"Healing attempts: {metrics['healing_attempts']}")
print(f"Healing successes: {metrics['healing_successes']}")
print(f"Success rate: {metrics['healing_attempts'] / metrics['healing_successes'] * 100:.1f}%")
print(f"AI suggestions used: {metrics['ai_suggestions_used']}")
```

### Logging Configuration

Enable detailed AI healing logs:

```python
import logging

# Configure AI healing logger
ai_logger = logging.getLogger('alumnium_integration')
ai_logger.setLevel(logging.INFO)

# Add handler for AI healing logs
handler = logging.FileHandler('ai_healing.log')
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
ai_logger.addHandler(handler)
```

## Troubleshooting

### Common Issues

#### 1. AI Agent Not Available
```
Error: "AI healing agent not available"
```

**Solution:**
- Check if Alumnium is properly installed: `pip install alumnium`
- Verify configuration in `alumnium_integration.py`
- Check if the model provider is accessible

#### 2. Low Healing Success Rate
```
Warning: "AI healing success rate below 50%"
```

**Solutions:**
- Lower the confidence threshold: `healing_confidence_threshold=0.5`
- Provide more descriptive context information
- Use more descriptive locators
- Check if the model is appropriate for your use case

#### 3. Slow AI Response
```
Warning: "AI healing taking longer than expected"
```

**Solutions:**
- Use lighter models: `model_name="microsoft/DialoGPT-small"`
- Reduce max healing attempts: `max_healing_attempts=2`
- Enable caching: Healing results are automatically cached

#### 4. Model Loading Issues
```
Error: "Failed to load AI model"
```

**Solutions:**
- Check internet connection for cloud models
- Verify API keys for commercial providers
- Use local models for offline operation
- Check model name spelling and availability

### Debug Mode

Enable debug mode for detailed troubleshooting:

```python
# Enable debug logging
import logging
logging.getLogger('alumnium_integration').setLevel(logging.DEBUG)

# Enable AI agent debug mode
alumnium_config.debug_mode = True
```

## Integration with Existing Framework

### Automatic Integration Points

The AI healing is automatically integrated at these points:

1. **Element Finding**: `enhanced_element_finder.py` - Strategy 4
2. **Action Execution**: All action classes inherit healing capabilities
3. **Session Management**: Automatic session recovery
4. **Error Handling**: Integrated into base action error handling

### Manual Integration

For custom actions, integrate AI healing:

```python
from app_android.utils.alumnium_integration import get_alumnium_agent

class CustomAction(BaseAction):
    def execute(self, params):
        try:
            # Your action logic here
            return self.perform_action(params)
        except Exception as e:
            # AI healing integration
            alumnium_agent = get_alumnium_agent()
            if alumnium_agent:
                healing_result = alumnium_agent.heal_element_locator(
                    original_locator=params.get('locator'),
                    locator_type=params.get('locator_type'),
                    context=f"Custom action: {self.__class__.__name__}"
                )
                if healing_result:
                    # Retry with healed locator
                    return self.retry_with_healed_locator(healing_result)
            
            # Fallback to original error handling
            raise e
```

## Security and Privacy

### Data Handling
- **No Sensitive Data**: The AI system only processes locator information and screen structure
- **Local Processing**: When using local models, no data leaves your environment
- **API Security**: When using cloud providers, ensure API keys are properly secured

### Best Practices
- Use environment variables for API keys
- Implement proper access controls
- Monitor AI usage for compliance
- Regular security audits of AI integration

## Open-Source Model Configuration Guide

### Recommended Open-Source Models

#### 1. Microsoft DialoGPT (Default)
```python
# Lightweight conversational model - good for general healing
alumnium_config = AlumniumConfig(
    model_provider="huggingface",
    model_name="microsoft/DialoGPT-medium",
    # Pros: Fast, no API key needed, good general performance
    # Cons: Limited context understanding
)
```

#### 2. Google Flan-T5
```python
# Instruction-following model - excellent for structured tasks
alumnium_config = AlumniumConfig(
    model_provider="huggingface",
    model_name="google/flan-t5-base",
    # Pros: Great for structured locator healing, instruction following
    # Cons: Larger model size
)
```

#### 3. Hugging Face CodeT5
```python
# Code-aware model - good for XPath and technical locators
alumnium_config = AlumniumConfig(
    model_provider="huggingface",
    model_name="Salesforce/codet5-base",
    # Pros: Understands code patterns, good for XPath healing
    # Cons: Specialized for code, may not handle natural language well
)
```

### Local Model Setup

#### Step 1: Install Dependencies
```bash
pip install transformers torch torchvision torchaudio
pip install accelerate  # For faster model loading
```

#### Step 2: Download Models Locally
```python
from transformers import AutoTokenizer, AutoModelForCausalLM

# Download and cache model locally
model_name = "microsoft/DialoGPT-medium"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)

# Models will be cached in ~/.cache/huggingface/
```

#### Step 3: Configure for Offline Use
```python
# Set offline mode
import os
os.environ["TRANSFORMERS_OFFLINE"] = "1"

alumnium_config = AlumniumConfig(
    model_provider="huggingface",
    model_name="microsoft/DialoGPT-medium",
    offline_mode=True,
    cache_dir="./models"  # Local model cache
)
```

### Performance Optimization for Open-Source Models

#### Model Size vs Performance
```python
# Small models (faster, less accurate)
small_config = AlumniumConfig(
    model_name="microsoft/DialoGPT-small",
    healing_confidence_threshold=0.5  # Lower threshold
)

# Medium models (balanced)
medium_config = AlumniumConfig(
    model_name="microsoft/DialoGPT-medium",
    healing_confidence_threshold=0.6
)

# Large models (slower, more accurate)
large_config = AlumniumConfig(
    model_name="microsoft/DialoGPT-large",
    healing_confidence_threshold=0.7
)
```

#### GPU Acceleration
```python
# Enable GPU if available
import torch

alumnium_config = AlumniumConfig(
    model_provider="huggingface",
    model_name="microsoft/DialoGPT-medium",
    device="cuda" if torch.cuda.is_available() else "cpu",
    torch_dtype=torch.float16  # Use half precision for speed
)
```

## Conclusion

The AI-powered test healing system provides robust, intelligent recovery capabilities that significantly improve test reliability and reduce maintenance overhead. By following this guide and best practices, you can leverage AI to create more resilient and self-healing test automation.
