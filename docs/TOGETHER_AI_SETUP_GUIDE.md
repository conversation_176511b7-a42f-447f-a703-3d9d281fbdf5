# Together AI Setup Guide for Mobile App Automation

## Overview

This guide shows you how to set up AI-powered test healing using Together AI's free models. Together AI provides free access to powerful open-source models like Llama 2, Mistral, and others without requiring a credit card.

## Why Together AI?

- **Free Access**: No credit card required for free tier
- **Powerful Models**: Access to Llama 2, Mistral, and other state-of-the-art models
- **High Rate Limits**: Generous free tier limits suitable for automation testing
- **Easy Integration**: OpenAI-compatible API for seamless integration
- **No Local Setup**: Cloud-based, no need to download large models locally

## Step 1: Get Your Free Together AI API Key

1. **Visit Together AI**: Go to https://api.together.xyz/
2. **Sign Up**: Create a free account (no credit card required)
3. **Get API Key**: Navigate to your dashboard and copy your API key
4. **Save API Key**: You'll need this for the next step

## Step 2: Configure Environment Variables

### Option A: Automatic Setup (Recommended)
```bash
# Run the automated setup script
python setup_ai_healing.py
```

This will:
- Install required dependencies
- Create a `.env` file template
- Set up the configuration files

### Option B: Manual Setup

1. **Create .env file** in your project root:
```bash
# Copy the template
cp .env.template .env
```

2. **Edit .env file** and add your API key:
```bash
# Together AI Configuration
TOGETHER_API_KEY=your_actual_api_key_here
TOGETHER_BASE_URL=https://api.together.xyz/v1

# AI Model Configuration
AI_MODEL_PROVIDER=together
AI_MODEL_NAME=meta-llama/Llama-2-7b-chat-hf
```

## Step 3: Choose Your Model

Together AI offers several free models. Here are the recommended options:

### Recommended Models

#### 1. Llama 2 7B Chat (Default)
```bash
AI_MODEL_NAME=meta-llama/Llama-2-7b-chat-hf
```
- **Best for**: General purpose, good balance of speed and accuracy
- **Use case**: Most automation scenarios
- **Performance**: Fast response times, good healing accuracy

#### 2. Llama 2 13B Chat (High Accuracy)
```bash
AI_MODEL_NAME=meta-llama/Llama-2-13b-chat-hf
```
- **Best for**: Complex applications, higher accuracy needs
- **Use case**: Critical automation where accuracy is paramount
- **Performance**: Slower but more accurate

#### 3. Mistral 7B Instruct (Fast)
```bash
AI_MODEL_NAME=mistralai/Mistral-7B-Instruct-v0.1
```
- **Best for**: Speed-critical scenarios
- **Use case**: High-frequency testing, quick feedback loops
- **Performance**: Very fast, good accuracy

#### 4. RedPajama 7B Chat (Lightweight)
```bash
AI_MODEL_NAME=togethercomputer/RedPajama-INCITE-7B-Chat
```
- **Best for**: Resource-constrained environments
- **Use case**: Basic healing scenarios
- **Performance**: Fastest, moderate accuracy

## Step 4: Install Dependencies

```bash
# Install required packages
pip install python-dotenv requests openai anthropic

# Install Alumnium framework
pip install git+https://github.com/alumnium-hq/alumnium.git
```

## Step 5: Verify Setup

### Test Configuration
```python
# Test script to verify setup
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check if API key is loaded
api_key = os.getenv('TOGETHER_API_KEY')
if api_key:
    print("✓ Together AI API key loaded successfully")
    print(f"✓ API key starts with: {api_key[:8]}...")
else:
    print("✗ Together AI API key not found")
    print("Please check your .env file")

# Test AI integration
try:
    from app_android.utils.alumnium_integration import create_default_config
    config = create_default_config(use_together_ai=True)
    print(f"✓ AI configuration created: {config.model_provider}/{config.model_name}")
except ImportError as e:
    print(f"✗ Import error: {e}")
```

### Run Validation Tests
```bash
# Run the comprehensive test suite
python -m pytest tests/test_ai_healing_integration.py -v

# Run performance measurement
python tools/performance_measurement.py
```

## Step 6: Usage Examples

### Basic Usage (Automatic)
```python
# AI healing is automatically integrated into element finding
element = controller.find_element_intelligently(
    locator_type="id",
    locator_value="submit_button",
    context="form_submission"
)
# If the locator fails, Together AI will automatically suggest alternatives
```

### Manual Healing
```python
from app_android.actions.ai_healing_action import AIHealingAction

# Create healing action
healing_action = AIHealingAction(controller)

# Perform healing
result = healing_action.execute({
    'healing_type': 'locator_healing',
    'failure_context': {
        'locator_type': 'id',
        'locator_value': 'login_button',
        'error_message': 'Element not found',
        'context': 'login form'
    }
})

if result['status'] == 'success':
    print(f"Healing successful: {result['message']}")
    healed_locator = result['healing_result']['healed_locator']
    print(f"New locator: {healed_locator['type']}='{healed_locator['value']}'")
```

### Custom Model Configuration
```python
from app_android.utils.alumnium_integration import create_together_ai_config

# Use Mistral model for faster responses
config = create_together_ai_config("mistralai/Mistral-7B-Instruct-v0.1")

# Use Llama 2 13B for higher accuracy
config = create_together_ai_config("meta-llama/Llama-2-13b-chat-hf")
```

## Performance Optimization

### Model Selection Guidelines

| Model | Speed | Accuracy | Best For |
|-------|-------|----------|----------|
| RedPajama 7B | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | High-frequency testing |
| Mistral 7B | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | Balanced performance |
| Llama 2 7B | ⭐⭐⭐ | ⭐⭐⭐⭐ | General purpose (default) |
| Llama 2 13B | ⭐⭐ | ⭐⭐⭐⭐⭐ | Critical applications |

### Rate Limits and Optimization

Together AI free tier includes:
- **Generous rate limits** for testing scenarios
- **No daily caps** for most models
- **Fair usage policy** applies

To optimize usage:
```python
# Configure for optimal performance
alumnium_config = AlumniumConfig(
    model_provider="together",
    model_name="meta-llama/Llama-2-7b-chat-hf",
    max_healing_attempts=2,  # Limit attempts to reduce API calls
    healing_confidence_threshold=0.7,  # Higher threshold for better results
    fallback_to_original=True  # Always have fallback
)
```

## Troubleshooting

### Common Issues

#### 1. API Key Not Working
```
Error: "Authentication failed"
```
**Solutions:**
- Verify API key is correct in .env file
- Check for extra spaces or quotes around the API key
- Ensure .env file is in the project root directory

#### 2. Model Not Found
```
Error: "Model not available"
```
**Solutions:**
- Check model name spelling
- Verify model is available on Together AI
- Try a different model from the recommended list

#### 3. Rate Limit Exceeded
```
Error: "Rate limit exceeded"
```
**Solutions:**
- Reduce `max_healing_attempts` in configuration
- Increase delays between requests
- Consider upgrading to paid tier for higher limits

#### 4. Slow Response Times
```
Warning: "AI response taking longer than expected"
```
**Solutions:**
- Switch to a faster model (RedPajama or Mistral)
- Reduce context length in prompts
- Check your internet connection

### Debug Mode

Enable debug logging to troubleshoot issues:
```python
import logging
logging.getLogger('alumnium_integration').setLevel(logging.DEBUG)

# Add to .env file
LOG_LEVEL=DEBUG
AI_HEALING_LOG_LEVEL=DEBUG
```

## Security Best Practices

1. **Never commit .env files** to version control
2. **Use different API keys** for development and production
3. **Rotate API keys** regularly
4. **Monitor API usage** in Together AI dashboard
5. **Set up alerts** for unusual usage patterns

## Cost Management

Together AI free tier is generous, but to manage usage:

1. **Monitor Usage**: Check your Together AI dashboard regularly
2. **Set Limits**: Configure `max_healing_attempts` appropriately
3. **Use Caching**: Enable healing result caching
4. **Choose Efficient Models**: Use smaller models for non-critical scenarios

## Support and Resources

- **Together AI Documentation**: https://docs.together.ai/
- **Together AI Discord**: Join their community for support
- **Model Playground**: Test models at https://api.together.xyz/playground
- **Status Page**: Check service status at https://status.together.ai/

## Next Steps

1. **Complete Setup**: Follow this guide to set up Together AI
2. **Run Tests**: Validate your setup with the test suite
3. **Monitor Performance**: Use the performance measurement tools
4. **Optimize Configuration**: Tune settings based on your needs
5. **Scale Usage**: Consider paid tier for production workloads

## Conclusion

Together AI provides an excellent free option for AI-powered test healing. With generous rate limits and powerful models, it's perfect for mobile automation testing. The setup is straightforward, and the integration provides immediate benefits in test reliability and maintenance reduction.
