"""
AI-Powered Test Healing Action

This action provides intelligent test healing capabilities using the Alumnium AI framework.
It can automatically recover from test failures, heal broken locators, and suggest alternative approaches.
"""

import logging
import time
from typing import Dict, Any, Optional, List
from .base_action import BaseAction

logger = logging.getLogger(__name__)


class AIHealingAction(BaseAction):
    """
    AI-powered test healing action that can recover from various test failures
    """
    
    def __init__(self, controller=None):
        """
        Initialize the AI healing action
        
        Args:
            controller: The device controller instance
        """
        super().__init__(controller)
        self.alumnium_agent = None
        self._initialize_ai_agent()
    
    def _initialize_ai_agent(self):
        """Initialize the Alumnium AI agent"""
        try:
            from ..utils.alumnium_integration import get_alumnium_agent
            self.alumnium_agent = get_alumnium_agent()
            if self.alumnium_agent and self.alumnium_agent.is_available():
                self.logger.info("AI healing agent initialized successfully")
            else:
                self.logger.warning("AI healing agent not available")
        except ImportError:
            self.logger.warning("Alumnium integration not available")
    
    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute AI-powered healing based on the failure type and context
        
        Args:
            params: Dictionary containing:
                - healing_type: Type of healing (locator, timeout, session, etc.)
                - failure_context: Context about the failure
                - original_action: The original action that failed
                - max_attempts: Maximum healing attempts (default: 3)
                
        Returns:
            dict: Result of the healing attempt
        """
        try:
            healing_type = params.get('healing_type', 'auto_detect')
            failure_context = params.get('failure_context', {})
            original_action = params.get('original_action', {})
            max_attempts = params.get('max_attempts', 3)
            
            self.logger.info(f"Starting AI healing for type: {healing_type}")
            
            if not self.alumnium_agent or not self.alumnium_agent.is_available():
                return {
                    'status': 'error',
                    'message': 'AI healing agent not available',
                    'healing_attempted': False
                }
            
            # Auto-detect healing type if not specified
            if healing_type == 'auto_detect':
                healing_type = self._detect_healing_type(failure_context, original_action)
            
            # Perform healing based on type
            healing_result = None
            if healing_type == 'locator_healing':
                healing_result = self._perform_locator_healing(failure_context, max_attempts)
            elif healing_type == 'timeout_healing':
                healing_result = self._perform_timeout_healing(failure_context, max_attempts)
            elif healing_type == 'session_healing':
                healing_result = self._perform_session_healing(failure_context, max_attempts)
            elif healing_type == 'action_suggestion':
                healing_result = self._perform_action_suggestion(failure_context, max_attempts)
            else:
                healing_result = self._perform_general_healing(failure_context, max_attempts)
            
            if healing_result and healing_result.get('success'):
                return {
                    'status': 'success',
                    'message': f'AI healing successful: {healing_result.get("message", "")}',
                    'healing_type': healing_type,
                    'healing_result': healing_result,
                    'healing_attempted': True
                }
            else:
                return {
                    'status': 'error',
                    'message': f'AI healing failed: {healing_result.get("message", "Unknown error")}',
                    'healing_type': healing_type,
                    'healing_result': healing_result,
                    'healing_attempted': True
                }
                
        except Exception as e:
            self.logger.error(f"Error during AI healing: {e}")
            return {
                'status': 'error',
                'message': f'AI healing error: {str(e)}',
                'healing_attempted': True
            }
    
    def _detect_healing_type(self, failure_context: Dict[str, Any], 
                           original_action: Dict[str, Any]) -> str:
        """
        Auto-detect the type of healing needed based on failure context
        
        Args:
            failure_context: Context about the failure
            original_action: The original action that failed
            
        Returns:
            str: Detected healing type
        """
        error_message = failure_context.get('error_message', '').lower()
        action_type = original_action.get('action_type', '').lower()
        
        # Detect based on error patterns
        if any(pattern in error_message for pattern in [
            'element not found', 'no such element', 'unable to locate'
        ]):
            return 'locator_healing'
        
        elif any(pattern in error_message for pattern in [
            'timeout', 'timed out', 'wait timeout'
        ]):
            return 'timeout_healing'
        
        elif any(pattern in error_message for pattern in [
            'session', 'connection', 'driver'
        ]):
            return 'session_healing'
        
        elif action_type in ['tap', 'click', 'input_text', 'swipe']:
            return 'locator_healing'  # Most common for action failures
        
        else:
            return 'general_healing'
    
    def _perform_locator_healing(self, failure_context: Dict[str, Any], 
                               max_attempts: int) -> Dict[str, Any]:
        """
        Perform AI-powered locator healing
        
        Args:
            failure_context: Context about the locator failure
            max_attempts: Maximum healing attempts
            
        Returns:
            dict: Healing result
        """
        try:
            locator_type = failure_context.get('locator_type', '')
            locator_value = failure_context.get('locator_value', '')
            context = failure_context.get('context', '')
            
            if not locator_type or not locator_value:
                return {
                    'success': False,
                    'message': 'Insufficient locator information for healing'
                }
            
            self.logger.info(f"Attempting AI locator healing for: {locator_type}='{locator_value}'")
            
            # Use Alumnium agent to heal the locator
            healing_result = self.alumnium_agent.heal_element_locator(
                original_locator=locator_value,
                locator_type=locator_type,
                context=context
            )
            
            if healing_result and healing_result.get('healed_locators'):
                healed_locators = healing_result['healed_locators']
                
                # Try each healed locator
                for attempt in range(max_attempts):
                    if attempt < len(healed_locators):
                        healed_locator = healed_locators[attempt]
                        
                        # Test the healed locator
                        test_result = self._test_healed_locator(healed_locator)
                        if test_result['success']:
                            return {
                                'success': True,
                                'message': f'Locator healed successfully on attempt {attempt + 1}',
                                'healed_locator': healed_locator,
                                'original_locator': {'type': locator_type, 'value': locator_value},
                                'attempts_used': attempt + 1
                            }
                
                return {
                    'success': False,
                    'message': f'All {len(healed_locators)} healed locators failed testing',
                    'healed_locators_tried': healed_locators
                }
            else:
                return {
                    'success': False,
                    'message': 'AI agent could not generate healed locators'
                }
                
        except Exception as e:
            self.logger.error(f"Error in locator healing: {e}")
            return {
                'success': False,
                'message': f'Locator healing error: {str(e)}'
            }
    
    def _test_healed_locator(self, healed_locator: Dict[str, Any]) -> Dict[str, Any]:
        """
        Test a healed locator to see if it works
        
        Args:
            healed_locator: The healed locator to test
            
        Returns:
            dict: Test result
        """
        try:
            locator_type = healed_locator.get('type', '')
            locator_value = healed_locator.get('value', '')
            
            if not self.controller or not hasattr(self.controller, 'find_element_intelligently'):
                return {'success': False, 'message': 'Controller not available for testing'}
            
            # Try to find the element using the healed locator
            element = self.controller.find_element_intelligently(
                locator_type=locator_type,
                locator_value=locator_value,
                timeout=5,  # Short timeout for testing
                context='ai_healing_test'
            )
            
            if element:
                return {
                    'success': True,
                    'message': 'Healed locator successfully found element',
                    'element_found': True
                }
            else:
                return {
                    'success': False,
                    'message': 'Healed locator did not find element',
                    'element_found': False
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'Error testing healed locator: {str(e)}'
            }
    
    def _perform_timeout_healing(self, failure_context: Dict[str, Any], 
                               max_attempts: int) -> Dict[str, Any]:
        """
        Perform AI-powered timeout healing with intelligent wait strategies
        
        Args:
            failure_context: Context about the timeout failure
            max_attempts: Maximum healing attempts
            
        Returns:
            dict: Healing result
        """
        try:
            element_description = failure_context.get('element_description', '')
            original_timeout = failure_context.get('timeout', 30)
            
            # Get AI suggestions for intelligent wait strategy
            wait_strategy = self.alumnium_agent.create_intelligent_wait_strategy(
                element_description=element_description,
                context='timeout_healing'
            )
            
            if wait_strategy:
                return {
                    'success': True,
                    'message': 'AI-generated intelligent wait strategy created',
                    'wait_strategy': wait_strategy,
                    'recommended_timeout': wait_strategy.get('timeout', original_timeout * 1.5)
                }
            else:
                return {
                    'success': False,
                    'message': 'Could not generate intelligent wait strategy'
                }
                
        except Exception as e:
            self.logger.error(f"Error in timeout healing: {e}")
            return {
                'success': False,
                'message': f'Timeout healing error: {str(e)}'
            }
    
    def _perform_session_healing(self, failure_context: Dict[str, Any], 
                               max_attempts: int) -> Dict[str, Any]:
        """
        Perform session healing and recovery
        
        Args:
            failure_context: Context about the session failure
            max_attempts: Maximum healing attempts
            
        Returns:
            dict: Healing result
        """
        try:
            # Get AI-powered recovery strategy
            recovery_strategy = self.alumnium_agent.implement_failure_recovery(
                failure_type='session_lost',
                failure_context=failure_context
            )
            
            if recovery_strategy:
                return {
                    'success': True,
                    'message': 'AI-generated session recovery strategy created',
                    'recovery_strategy': recovery_strategy
                }
            else:
                return {
                    'success': False,
                    'message': 'Could not generate session recovery strategy'
                }
                
        except Exception as e:
            self.logger.error(f"Error in session healing: {e}")
            return {
                'success': False,
                'message': f'Session healing error: {str(e)}'
            }
    
    def _perform_action_suggestion(self, failure_context: Dict[str, Any], 
                                 max_attempts: int) -> Dict[str, Any]:
        """
        Get AI suggestions for alternative actions
        
        Args:
            failure_context: Context about the action failure
            max_attempts: Maximum attempts
            
        Returns:
            dict: Healing result with action suggestions
        """
        try:
            goal = failure_context.get('goal', 'Complete the intended action')
            
            # Get AI action suggestions
            action_suggestions = self.alumnium_agent.suggest_actions(goal)
            
            if action_suggestions:
                return {
                    'success': True,
                    'message': f'AI generated {len(action_suggestions)} alternative action suggestions',
                    'action_suggestions': action_suggestions
                }
            else:
                return {
                    'success': False,
                    'message': 'Could not generate action suggestions'
                }
                
        except Exception as e:
            self.logger.error(f"Error in action suggestion: {e}")
            return {
                'success': False,
                'message': f'Action suggestion error: {str(e)}'
            }
    
    def _perform_general_healing(self, failure_context: Dict[str, Any], 
                               max_attempts: int) -> Dict[str, Any]:
        """
        Perform general AI-powered healing for unspecified failures
        
        Args:
            failure_context: Context about the failure
            max_attempts: Maximum healing attempts
            
        Returns:
            dict: Healing result
        """
        try:
            # Try multiple healing approaches
            healing_attempts = []
            
            # Attempt 1: Locator healing if locator info available
            if failure_context.get('locator_type') and failure_context.get('locator_value'):
                locator_result = self._perform_locator_healing(failure_context, 1)
                healing_attempts.append(('locator_healing', locator_result))
                if locator_result.get('success'):
                    return locator_result
            
            # Attempt 2: Action suggestions
            action_result = self._perform_action_suggestion(failure_context, 1)
            healing_attempts.append(('action_suggestion', action_result))
            if action_result.get('success'):
                return action_result
            
            # Attempt 3: Timeout healing
            timeout_result = self._perform_timeout_healing(failure_context, 1)
            healing_attempts.append(('timeout_healing', timeout_result))
            if timeout_result.get('success'):
                return timeout_result
            
            return {
                'success': False,
                'message': 'All general healing attempts failed',
                'healing_attempts': healing_attempts
            }
            
        except Exception as e:
            self.logger.error(f"Error in general healing: {e}")
            return {
                'success': False,
                'message': f'General healing error: {str(e)}'
            }
