"""
Alumnium AI Framework Integration for Mobile App Automation

This module provides integration between the Alumnium AI framework and the existing
mobile automation platform, enabling AI-powered test healing and intelligent automation.
"""

import logging
import time
import json
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

# Alumnium imports with fallback handling
try:
    from alumnium import Alumni
    from alumnium.drivers import AppiumDriver as AlumniumAppiumDriver
    from alumnium.accessibility import UIAutomator2AccessibiltyTree, XCUITestAccessibilityTree
    from alumnium.models import Model
    ALUMNIUM_AVAILABLE = True
except ImportError:
    ALUMNIUM_AVAILABLE = False
    Alumni = None
    AlumniumAppiumDriver = None
    UIAutomator2AccessibiltyTree = None
    XCUITestAccessibilityTree = None
    Model = None

logger = logging.getLogger(__name__)


@dataclass
class AlumniumConfig:
    """Configuration for Alumnium AI integration"""
    enabled: bool = True
    model_provider: str = "openai"  # openai, anthropic, huggingface
    model_name: str = "gpt-3.5-turbo"
    api_key: Optional[str] = None
    healing_enabled: bool = True
    intelligent_waits: bool = True
    action_suggestions: bool = True
    fallback_to_original: bool = True
    max_healing_attempts: int = 3
    healing_confidence_threshold: float = 0.7


class AlumniumMobileAgent:
    """
    AI-powered mobile test agent using Alumnium framework
    Provides intelligent test healing, element finding, and action execution
    """
    
    def __init__(self, device_controller, config: Optional[AlumniumConfig] = None):
        """
        Initialize the Alumnium mobile agent
        
        Args:
            device_controller: The existing device controller instance
            config: Alumnium configuration settings
        """
        self.device_controller = device_controller
        self.config = config or AlumniumConfig()
        self.alumni_instance = None
        self.healing_cache = {}
        self.performance_metrics = {
            'healing_attempts': 0,
            'healing_successes': 0,
            'ai_suggestions_used': 0,
            'fallback_to_original': 0
        }
        
        # Initialize Alumnium if available and enabled
        if ALUMNIUM_AVAILABLE and self.config.enabled:
            self._initialize_alumnium()
        else:
            logger.warning("Alumnium not available or disabled, using fallback mode")
    
    def _initialize_alumnium(self):
        """Initialize the Alumnium AI framework"""
        try:
            # Configure the AI model
            if self.config.api_key:
                import os
                os.environ["OPENAI_API_KEY"] = self.config.api_key
            
            # Create Alumnium instance with the device controller's driver
            if hasattr(self.device_controller, 'driver') and self.device_controller.driver:
                # Configure model if specified
                model = None
                if self.config.model_provider and self.config.model_name:
                    model = Model(
                        provider=self.config.model_provider,
                        name=self.config.model_name
                    )
                
                self.alumni_instance = Alumni(
                    driver=self.device_controller.driver,
                    model=model
                )
                logger.info("Alumnium AI agent initialized successfully")
            else:
                logger.error("Device controller driver not available for Alumnium initialization")
                
        except Exception as e:
            logger.error(f"Failed to initialize Alumnium: {e}")
            self.alumni_instance = None
    
    def is_available(self) -> bool:
        """Check if Alumnium AI agent is available and ready"""
        return self.alumni_instance is not None
    
    def heal_element_locator(self, original_locator: str, locator_type: str, 
                           context: str = None) -> Optional[Dict[str, Any]]:
        """
        Use AI to heal a failed element locator
        
        Args:
            original_locator: The original locator that failed
            locator_type: Type of locator (xpath, id, accessibility_id, etc.)
            context: Context about what the element should do
            
        Returns:
            Dictionary with healed locator information or None if healing failed
        """
        if not self.is_available() or not self.config.healing_enabled:
            return None
        
        self.performance_metrics['healing_attempts'] += 1
        
        try:
            # Check healing cache first
            cache_key = f"{locator_type}:{original_locator}"
            if cache_key in self.healing_cache:
                cached_result = self.healing_cache[cache_key]
                if time.time() - cached_result['timestamp'] < 300:  # 5 minutes cache
                    logger.debug(f"Using cached healing result for {cache_key}")
                    return cached_result['result']
            
            # Use Alumnium to analyze the current screen and suggest alternatives
            healing_prompt = self._create_healing_prompt(original_locator, locator_type, context)
            
            # Get AI suggestions for element location
            suggestions = self.alumni_instance.get(healing_prompt)
            
            if suggestions and isinstance(suggestions, (list, str)):
                healed_locators = self._parse_healing_suggestions(suggestions, locator_type)
                
                if healed_locators:
                    result = {
                        'original_locator': original_locator,
                        'healed_locators': healed_locators,
                        'confidence': self._calculate_healing_confidence(healed_locators),
                        'healing_method': 'ai_analysis'
                    }
                    
                    # Cache the result
                    self.healing_cache[cache_key] = {
                        'result': result,
                        'timestamp': time.time()
                    }
                    
                    self.performance_metrics['healing_successes'] += 1
                    logger.info(f"Successfully healed locator: {original_locator}")
                    return result
            
        except Exception as e:
            logger.error(f"Error during AI healing: {e}")
        
        return None
    
    def suggest_intelligent_wait(self, element_description: str, 
                               timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        Use AI to suggest intelligent wait strategies
        
        Args:
            element_description: Description of what to wait for
            timeout: Maximum timeout in seconds
            
        Returns:
            Dictionary with wait strategy suggestions
        """
        if not self.is_available() or not self.config.intelligent_waits:
            return None
        
        try:
            wait_prompt = f"""
            Analyze the current mobile app screen and suggest the best wait strategy for: {element_description}
            
            Consider:
            1. Network loading indicators
            2. Animation states
            3. Element visibility patterns
            4. App-specific loading behaviors
            
            Provide specific wait conditions and estimated timeouts.
            """
            
            suggestions = self.alumni_instance.get(wait_prompt)
            
            if suggestions:
                return {
                    'strategy': 'ai_intelligent_wait',
                    'suggestions': suggestions,
                    'max_timeout': timeout,
                    'confidence': 0.8
                }
                
        except Exception as e:
            logger.error(f"Error getting intelligent wait suggestions: {e}")
        
        return None
    
    def suggest_actions(self, goal: str) -> Optional[List[Dict[str, Any]]]:
        """
        Use AI to suggest actions to achieve a goal
        
        Args:
            goal: Natural language description of what to achieve
            
        Returns:
            List of suggested actions
        """
        if not self.is_available() or not self.config.action_suggestions:
            return None
        
        try:
            self.performance_metrics['ai_suggestions_used'] += 1
            
            # Use Alumnium's do method to get action suggestions
            action_plan = self.alumni_instance.do(goal)
            
            if action_plan:
                return self._convert_alumnium_actions(action_plan)
                
        except Exception as e:
            logger.error(f"Error getting action suggestions: {e}")
        
        return None
    
    def verify_with_ai(self, assertion: str) -> Optional[bool]:
        """
        Use AI to verify an assertion about the current screen
        
        Args:
            assertion: Natural language assertion to verify
            
        Returns:
            True if assertion is verified, False if not, None if AI unavailable
        """
        if not self.is_available():
            return None
        
        try:
            result = self.alumni_instance.check(assertion, vision=True)
            return bool(result)
            
        except Exception as e:
            logger.error(f"Error during AI verification: {e}")
            return None
    
    def _create_healing_prompt(self, locator: str, locator_type: str, context: str) -> str:
        """Create a prompt for AI-powered locator healing"""
        prompt = f"""
        The mobile app element locator '{locator}' of type '{locator_type}' has failed.
        
        Context: {context or 'No specific context provided'}
        
        Analyze the current screen and suggest alternative locators that could find the same element.
        Consider:
        1. Similar elements with different attributes
        2. Parent/child element relationships
        3. Text content or accessibility labels
        4. Position-based alternatives
        
        Provide 3-5 alternative locators in order of confidence.
        """
        return prompt
    
    def _parse_healing_suggestions(self, suggestions: Any, locator_type: str) -> List[Dict[str, Any]]:
        """Parse AI suggestions into structured locator alternatives"""
        # Implementation would parse the AI response and convert to structured format
        # This is a simplified version
        if isinstance(suggestions, str):
            # Parse string suggestions into structured format
            alternatives = []
            # Add parsing logic here based on AI response format
            return alternatives
        elif isinstance(suggestions, list):
            return suggestions
        return []
    
    def _calculate_healing_confidence(self, healed_locators: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for healed locators"""
        if not healed_locators:
            return 0.0
        
        # Simple confidence calculation based on number of alternatives
        base_confidence = min(0.9, len(healed_locators) * 0.2 + 0.5)
        return base_confidence
    
    def _convert_alumnium_actions(self, action_plan: Any) -> List[Dict[str, Any]]:
        """Convert Alumnium action plan to framework-compatible format"""
        # Implementation would convert Alumnium actions to the framework's action format
        actions = []
        # Add conversion logic here
        return actions
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the AI agent"""
        metrics = self.performance_metrics.copy()
        if metrics['healing_attempts'] > 0:
            metrics['healing_success_rate'] = metrics['healing_successes'] / metrics['healing_attempts']
        else:
            metrics['healing_success_rate'] = 0.0
        
        return metrics
    
    def cleanup(self):
        """Cleanup resources"""
        if self.alumni_instance:
            try:
                self.alumni_instance.quit()
            except Exception as e:
                logger.error(f"Error during Alumnium cleanup: {e}")


# Global instance for easy access
alumnium_agent = None


def initialize_alumnium_agent(device_controller, config: Optional[AlumniumConfig] = None):
    """Initialize the global Alumnium agent instance"""
    global alumnium_agent
    alumnium_agent = AlumniumMobileAgent(device_controller, config)
    return alumnium_agent


def get_alumnium_agent() -> Optional[AlumniumMobileAgent]:
    """Get the global Alumnium agent instance"""
    return alumnium_agent


def create_default_config(use_open_source: bool = True) -> AlumniumConfig:
    """
    Create a default Alumnium configuration

    Args:
        use_open_source: If True, configure for open-source models (Hugging Face)
                        If False, use OpenAI/Anthropic models

    Returns:
        AlumniumConfig with appropriate settings
    """
    if use_open_source:
        return AlumniumConfig(
            enabled=True,
            model_provider="huggingface",
            model_name="microsoft/DialoGPT-medium",  # Free open-source model
            api_key=None,  # No API key needed for local models
            healing_enabled=True,
            intelligent_waits=True,
            action_suggestions=True,
            fallback_to_original=True,
            max_healing_attempts=3,
            healing_confidence_threshold=0.6  # Lower threshold for open-source models
        )
    else:
        return AlumniumConfig(
            enabled=True,
            model_provider="openai",
            model_name="gpt-3.5-turbo",
            api_key=None,  # Should be set via environment variable
            healing_enabled=True,
            intelligent_waits=True,
            action_suggestions=True,
            fallback_to_original=True,
            max_healing_attempts=3,
            healing_confidence_threshold=0.7
        )
