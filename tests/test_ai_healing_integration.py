"""
Comprehensive Tests for AI-Powered Test Healing Integration

This test suite validates the AI healing functionality, performance optimizations,
and ensures existing automation capabilities remain unbroken.
"""

import unittest
import time
import logging
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestAlumniumIntegration(unittest.TestCase):
    """Test Alumnium AI framework integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_controller = Mock()
        self.mock_controller.driver = Mock()
        self.mock_controller.platform_name = "Android"
    
    def test_alumnium_config_creation(self):
        """Test AlumniumConfig creation with different settings"""
        try:
            from app_android.utils.alumnium_integration import AlumniumConfig, create_default_config
            
            # Test default config
            config = AlumniumConfig()
            self.assertTrue(config.enabled)
            self.assertEqual(config.model_provider, "openai")
            
            # Test open-source config
            os_config = create_default_config(use_open_source=True)
            self.assertEqual(os_config.model_provider, "huggingface")
            self.assertIsNone(os_config.api_key)
            
            # Test commercial config
            commercial_config = create_default_config(use_open_source=False)
            self.assertEqual(commercial_config.model_provider, "openai")
            
            logger.info("✓ AlumniumConfig creation tests passed")
            
        except ImportError:
            self.skipTest("Alumnium integration not available")
    
    def test_alumnium_agent_initialization(self):
        """Test AlumniumMobileAgent initialization"""
        try:
            from app_android.utils.alumnium_integration import AlumniumMobileAgent, AlumniumConfig
            
            config = AlumniumConfig(enabled=True)
            agent = AlumniumMobileAgent(self.mock_controller, config)
            
            self.assertIsNotNone(agent)
            self.assertEqual(agent.device_controller, self.mock_controller)
            self.assertEqual(agent.config, config)
            
            # Test performance metrics initialization
            metrics = agent.get_performance_metrics()
            self.assertIn('healing_attempts', metrics)
            self.assertIn('healing_successes', metrics)
            
            logger.info("✓ AlumniumMobileAgent initialization tests passed")
            
        except ImportError:
            self.skipTest("Alumnium integration not available")
    
    @patch('app_android.utils.alumnium_integration.Alumni')
    def test_locator_healing_functionality(self, mock_alumni):
        """Test AI-powered locator healing"""
        try:
            from app_android.utils.alumnium_integration import AlumniumMobileAgent, AlumniumConfig
            
            # Setup mock
            mock_alumni_instance = Mock()
            mock_alumni.return_value = mock_alumni_instance
            mock_alumni_instance.get.return_value = "xpath://button[@text='Submit']"
            
            config = AlumniumConfig(enabled=True)
            agent = AlumniumMobileAgent(self.mock_controller, config)
            agent.alumni_instance = mock_alumni_instance
            
            # Test locator healing
            healing_result = agent.heal_element_locator(
                original_locator="submit_btn",
                locator_type="id",
                context="form submission"
            )
            
            self.assertIsNotNone(healing_result)
            self.assertIn('original_locator', healing_result)
            self.assertIn('healed_locators', healing_result)
            
            logger.info("✓ Locator healing functionality tests passed")
            
        except ImportError:
            self.skipTest("Alumnium integration not available")


class TestPerformanceOptimizations(unittest.TestCase):
    """Test performance optimization implementations"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.start_time = time.time()
    
    def test_performance_config_loading(self):
        """Test performance configuration loading"""
        try:
            from app_android.config.performance_config import performance_config
            
            # Test configuration values
            self.assertGreaterEqual(performance_config.screenshot_frequency_limit, 5.0)
            self.assertGreaterEqual(performance_config.health_check_interval, 30.0)
            self.assertTrue(performance_config.screenshot_intelligent_context)
            
            # Test screenshot decision logic
            should_skip = not performance_config.should_take_screenshot(
                context='element_search'
            )
            self.assertTrue(should_skip, "Screenshots should be skipped during element search")
            
            logger.info("✓ Performance configuration tests passed")
            
        except ImportError:
            self.skipTest("Performance config not available")
    
    def test_screenshot_optimization(self):
        """Test screenshot optimization features"""
        try:
            from app_android.config.performance_config import performance_config
            
            # Test frequency limiting
            performance_config.record_screenshot_taken()
            time.sleep(0.1)  # Short delay
            
            should_take = performance_config.should_take_screenshot(
                action_id="test_action",
                context="action_execution"
            )
            
            # Should be limited by frequency
            self.assertFalse(should_take, "Screenshot should be limited by frequency")
            
            # Test per-minute limiting
            performance_config._screenshot_count_per_minute = [time.time()] * 15  # Exceed limit
            should_take_limited = performance_config.should_take_screenshot(
                context="action_execution"
            )
            self.assertFalse(should_take_limited, "Screenshot should be limited by per-minute cap")
            
            logger.info("✓ Screenshot optimization tests passed")
            
        except ImportError:
            self.skipTest("Performance config not available")
    
    def test_health_check_optimization(self):
        """Test health check optimization"""
        try:
            from app_android.utils.optimized_health_check_manager import OptimizedHealthCheckManager
            
            mock_controller = Mock()
            health_manager = OptimizedHealthCheckManager(mock_controller)
            
            # Test smart suspension
            health_manager.suspend_for_operation("screenshot_capture")
            self.assertEqual(health_manager.state.value, "suspended")
            
            health_manager.resume_after_operation("screenshot_capture")
            self.assertEqual(health_manager.state.value, "active")
            
            # Test metrics
            metrics = health_manager.get_metrics()
            self.assertIn('total_checks', metrics)
            self.assertIn('suspensions', metrics)
            
            logger.info("✓ Health check optimization tests passed")
            
        except ImportError:
            self.skipTest("Health check manager not available")


class TestEnhancedElementFinder(unittest.TestCase):
    """Test enhanced element finder with AI integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_controller = Mock()
        self.mock_controller.driver = Mock()
    
    def test_enhanced_element_finder_initialization(self):
        """Test enhanced element finder initialization"""
        try:
            from app_android.utils.enhanced_element_finder import EnhancedElementFinder
            
            finder = EnhancedElementFinder(self.mock_controller)
            self.assertIsNotNone(finder)
            self.assertEqual(finder.controller, self.mock_controller)
            
            logger.info("✓ Enhanced element finder initialization tests passed")
            
        except ImportError:
            self.skipTest("Enhanced element finder not available")
    
    @patch('app_android.utils.enhanced_element_finder.get_alumnium_agent')
    def test_ai_healing_integration(self, mock_get_agent):
        """Test AI healing integration in element finder"""
        try:
            from app_android.utils.enhanced_element_finder import EnhancedElementFinder
            
            # Setup mock AI agent
            mock_agent = Mock()
            mock_agent.is_available.return_value = True
            mock_agent.heal_element_locator.return_value = {
                'healed_locators': [
                    {'type': 'xpath', 'value': '//button[@text="Submit"]', 'confidence': 0.8}
                ]
            }
            mock_get_agent.return_value = mock_agent
            
            finder = EnhancedElementFinder(self.mock_controller)
            
            # Test AI healing method
            healing_result = finder._try_ai_healing(
                locator_type="id",
                locator_value="submit_btn",
                context="form_submission",
                timeout=10.0
            )
            
            # Should attempt healing
            mock_agent.heal_element_locator.assert_called_once()
            
            logger.info("✓ AI healing integration tests passed")
            
        except ImportError:
            self.skipTest("Enhanced element finder not available")


class TestAIHealingAction(unittest.TestCase):
    """Test AI healing action functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_controller = Mock()
    
    def test_ai_healing_action_initialization(self):
        """Test AI healing action initialization"""
        try:
            from app_android.actions.ai_healing_action import AIHealingAction
            
            action = AIHealingAction(self.mock_controller)
            self.assertIsNotNone(action)
            self.assertEqual(action.controller, self.mock_controller)
            
            logger.info("✓ AI healing action initialization tests passed")
            
        except ImportError:
            self.skipTest("AI healing action not available")
    
    def test_healing_type_detection(self):
        """Test automatic healing type detection"""
        try:
            from app_android.actions.ai_healing_action import AIHealingAction
            
            action = AIHealingAction(self.mock_controller)
            
            # Test element not found detection
            healing_type = action._detect_healing_type(
                failure_context={'error_message': 'Element not found'},
                original_action={'action_type': 'tap'}
            )
            self.assertEqual(healing_type, 'locator_healing')
            
            # Test timeout detection
            healing_type = action._detect_healing_type(
                failure_context={'error_message': 'Timeout waiting for element'},
                original_action={'action_type': 'wait'}
            )
            self.assertEqual(healing_type, 'timeout_healing')
            
            logger.info("✓ Healing type detection tests passed")
            
        except ImportError:
            self.skipTest("AI healing action not available")


class TestPerformanceValidation(unittest.TestCase):
    """Test performance improvements and validate optimizations"""
    
    def test_screenshot_frequency_reduction(self):
        """Test that screenshot frequency has been reduced"""
        try:
            from app_android.config.performance_config import performance_config
            
            # Verify frequency limit is optimized (should be >= 5.0 seconds)
            self.assertGreaterEqual(
                performance_config.screenshot_frequency_limit, 
                5.0,
                "Screenshot frequency should be optimized to at least 5 seconds"
            )
            
            # Verify per-minute limit exists
            self.assertIsNotNone(
                performance_config.screenshot_max_per_minute,
                "Per-minute screenshot limit should be configured"
            )
            
            logger.info("✓ Screenshot frequency reduction validated")
            
        except ImportError:
            self.skipTest("Performance config not available")
    
    def test_health_check_interval_optimization(self):
        """Test that health check intervals have been optimized"""
        try:
            from app_android.config.performance_config import performance_config
            
            # Verify health check interval is optimized (should be >= 30.0 seconds)
            self.assertGreaterEqual(
                performance_config.health_check_interval,
                30.0,
                "Health check interval should be optimized to at least 30 seconds"
            )
            
            logger.info("✓ Health check interval optimization validated")
            
        except ImportError:
            self.skipTest("Performance config not available")
    
    def test_performance_metrics_tracking(self):
        """Test that performance metrics are being tracked"""
        try:
            from app_android.config.performance_config import performance_config
            
            # Get performance metrics
            metrics = performance_config.get_performance_metrics()
            
            # Verify required metrics exist
            required_metrics = [
                'screenshots_skipped',
                'health_checks_optimized', 
                'total_time_saved_estimate'
            ]
            
            for metric in required_metrics:
                self.assertIn(metric, metrics, f"Required metric '{metric}' should be tracked")
            
            logger.info("✓ Performance metrics tracking validated")
            
        except ImportError:
            self.skipTest("Performance config not available")


class TestBackwardCompatibility(unittest.TestCase):
    """Test that existing automation functionality remains unbroken"""
    
    def test_existing_action_compatibility(self):
        """Test that existing actions still work with new integrations"""
        try:
            from app_android.actions.base_action import BaseAction
            
            # Test base action still works
            mock_controller = Mock()
            action = BaseAction(mock_controller)
            
            self.assertEqual(action.controller, mock_controller)
            self.assertIsNotNone(action.logger)
            
            logger.info("✓ Existing action compatibility validated")
            
        except ImportError:
            self.skipTest("Base action not available")
    
    def test_device_controller_compatibility(self):
        """Test that device controller functionality is preserved"""
        try:
            # Test that device controller can still be imported and initialized
            from app_android.utils.appium_device_controller import AppiumDeviceController
            
            # Should be able to create instance without errors
            controller = AppiumDeviceController()
            self.assertIsNotNone(controller)
            
            logger.info("✓ Device controller compatibility validated")
            
        except ImportError:
            self.skipTest("Device controller not available")


def run_performance_benchmark():
    """Run a simple performance benchmark to validate optimizations"""
    logger.info("Running performance benchmark...")
    
    try:
        from app_android.config.performance_config import performance_config
        
        # Simulate screenshot decisions
        start_time = time.time()
        decisions = []
        
        for i in range(100):
            decision = performance_config.should_take_screenshot(
                action_id=f"test_action_{i}",
                context="element_search"
            )
            decisions.append(decision)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should be very fast (< 0.1 seconds for 100 decisions)
        assert duration < 0.1, f"Screenshot decisions too slow: {duration:.3f}s"
        
        # Should skip most screenshots during element search
        skip_rate = decisions.count(False) / len(decisions)
        assert skip_rate > 0.8, f"Screenshot skip rate too low: {skip_rate:.2f}"
        
        logger.info(f"✓ Performance benchmark passed: {duration:.3f}s, skip rate: {skip_rate:.2f}")
        
    except Exception as e:
        logger.error(f"✗ Performance benchmark failed: {e}")


if __name__ == "__main__":
    # Run performance benchmark first
    run_performance_benchmark()
    
    # Run unit tests
    unittest.main(verbosity=2)
