#!/usr/bin/env python3
"""
AI-Powered Test Healing Setup Script

This script sets up the AI-powered test healing capabilities for the mobile automation framework.
It installs required dependencies, configures the Alumnium integration, and validates the setup.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    logger.info(f"Python version: {sys.version}")
    return True


def install_dependencies():
    """Install required dependencies for AI healing"""
    logger.info("Installing AI healing dependencies...")
    
    dependencies = [
        "transformers>=4.20.0",
        "torch>=1.12.0",
        "accelerate>=0.20.0",
        "huggingface-hub>=0.15.0"
    ]
    
    try:
        # Try to install Alumnium from GitHub
        logger.info("Installing Alumnium framework...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "git+https://github.com/alumnium-hq/alumnium.git"
        ])
        
        # Install other dependencies
        for dep in dependencies:
            logger.info(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        
        logger.info("All dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False


def setup_model_cache():
    """Setup local model cache directory"""
    cache_dir = Path.home() / ".cache" / "mobile_automation_ai"
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Model cache directory created: {cache_dir}")
    return str(cache_dir)


def download_default_model():
    """Download the default AI model for offline use"""
    logger.info("Downloading default AI model (microsoft/DialoGPT-medium)...")
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_name = "microsoft/DialoGPT-medium"
        
        # Download tokenizer
        logger.info("Downloading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        
        # Download model
        logger.info("Downloading model (this may take a few minutes)...")
        model = AutoModelForCausalLM.from_pretrained(model_name)
        
        logger.info("Default model downloaded and cached successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to download model: {e}")
        logger.info("Model will be downloaded on first use")
        return False


def create_config_file():
    """Create AI healing configuration file"""
    config_content = '''"""
AI Healing Configuration for Mobile Automation

This file contains the configuration for AI-powered test healing.
Modify these settings based on your requirements.
"""

from app_android.utils.alumnium_integration import AlumniumConfig

# Default configuration using open-source models
DEFAULT_AI_CONFIG = AlumniumConfig(
    enabled=True,
    model_provider="huggingface",
    model_name="microsoft/DialoGPT-medium",
    api_key=None,  # No API key needed for open-source models
    healing_enabled=True,
    intelligent_waits=True,
    action_suggestions=True,
    fallback_to_original=True,
    max_healing_attempts=3,
    healing_confidence_threshold=0.6
)

# Alternative configurations for different use cases

# High performance configuration (faster, less accurate)
FAST_AI_CONFIG = AlumniumConfig(
    enabled=True,
    model_provider="huggingface",
    model_name="microsoft/DialoGPT-small",
    healing_enabled=True,
    max_healing_attempts=2,
    healing_confidence_threshold=0.5
)

# High accuracy configuration (slower, more accurate)
ACCURATE_AI_CONFIG = AlumniumConfig(
    enabled=True,
    model_provider="huggingface", 
    model_name="google/flan-t5-base",
    healing_enabled=True,
    max_healing_attempts=3,
    healing_confidence_threshold=0.7
)

# OpenAI configuration (requires API key)
OPENAI_AI_CONFIG = AlumniumConfig(
    enabled=True,
    model_provider="openai",
    model_name="gpt-3.5-turbo",
    api_key=None,  # Set via environment variable OPENAI_API_KEY
    healing_enabled=True,
    max_healing_attempts=3,
    healing_confidence_threshold=0.7
)

# Use this configuration in your automation
CURRENT_AI_CONFIG = DEFAULT_AI_CONFIG
'''
    
    config_path = Path("config") / "ai_healing_config.py"
    config_path.parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    logger.info(f"Configuration file created: {config_path}")
    return str(config_path)


def validate_setup():
    """Validate that the AI healing setup is working"""
    logger.info("Validating AI healing setup...")
    
    try:
        # Test imports
        from app_android.utils.alumnium_integration import AlumniumConfig, AlumniumMobileAgent
        logger.info("✓ Alumnium integration imports successful")
        
        # Test configuration
        config = AlumniumConfig()
        logger.info("✓ Configuration creation successful")
        
        # Test basic functionality (without actual device controller)
        logger.info("✓ Basic AI healing functionality available")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ Setup validation failed: {e}")
        return False


def create_example_usage():
    """Create example usage file"""
    example_content = '''"""
Example Usage of AI-Powered Test Healing

This file demonstrates how to use the AI healing capabilities in your tests.
"""

from app_android.utils.alumnium_integration import initialize_alumnium_agent, create_default_config
from app_android.actions.ai_healing_action import AIHealingAction

def example_automatic_healing(device_controller):
    """Example of automatic AI healing during element finding"""
    
    # Initialize AI agent
    config = create_default_config(use_open_source=True)
    alumnium_agent = initialize_alumnium_agent(device_controller, config)
    
    # Normal element finding with automatic AI healing
    element = device_controller.find_element_intelligently(
        locator_type="id",
        locator_value="submit_button",
        timeout=30,
        context="form_submission"
    )
    # If locator fails, AI healing is automatically attempted
    
    return element


def example_manual_healing(device_controller):
    """Example of manual AI healing for failed actions"""
    
    # Create healing action
    healing_action = AIHealingAction(device_controller)
    
    # Define failure context
    healing_params = {
        'healing_type': 'locator_healing',
        'failure_context': {
            'locator_type': 'id',
            'locator_value': 'login_button',
            'error_message': 'Element not found after 30 seconds',
            'context': 'login form submission',
            'screen_context': 'login_page'
        },
        'original_action': {
            'action_type': 'tap',
            'target': 'login_button'
        },
        'max_attempts': 3
    }
    
    # Perform healing
    result = healing_action.execute(healing_params)
    
    if result['status'] == 'success':
        print(f"Healing successful: {result['message']}")
        healed_locator = result['healing_result']['healed_locator']
        print(f"New locator: {healed_locator['type']}='{healed_locator['value']}'")
    else:
        print(f"Healing failed: {result['message']}")
    
    return result


def example_intelligent_wait(device_controller):
    """Example of AI-powered intelligent wait strategies"""
    
    from app_android.utils.alumnium_integration import get_alumnium_agent
    
    # Get AI agent
    alumnium_agent = get_alumnium_agent()
    
    if alumnium_agent:
        # Create intelligent wait strategy
        wait_strategy = alumnium_agent.create_intelligent_wait_strategy(
            element_description="loading spinner for data refresh",
            context="data_loading_operation"
        )
        
        print(f"AI Wait Strategy: {wait_strategy}")
        
        # Apply wait conditions
        for condition in wait_strategy['conditions']:
            print(f"Applying condition: {condition['description']}")
            # Implement the wait condition based on type
    
    return wait_strategy


if __name__ == "__main__":
    print("AI Healing Examples")
    print("===================")
    print("This file contains examples of using AI-powered test healing.")
    print("Import these functions in your test scripts to use AI healing capabilities.")
'''
    
    example_path = Path("examples") / "ai_healing_examples.py"
    example_path.parent.mkdir(exist_ok=True)
    
    with open(example_path, 'w') as f:
        f.write(example_content)
    
    logger.info(f"Example usage file created: {example_path}")
    return str(example_path)


def main():
    """Main setup function"""
    logger.info("Starting AI-Powered Test Healing Setup")
    logger.info("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        logger.error("Failed to install dependencies")
        sys.exit(1)
    
    # Setup model cache
    cache_dir = setup_model_cache()
    
    # Download default model (optional)
    download_default_model()
    
    # Create configuration file
    config_path = create_config_file()
    
    # Create example usage
    example_path = create_example_usage()
    
    # Validate setup
    if validate_setup():
        logger.info("✓ AI healing setup completed successfully!")
        logger.info("")
        logger.info("Next Steps:")
        logger.info(f"1. Review configuration in: {config_path}")
        logger.info(f"2. Check examples in: {example_path}")
        logger.info("3. Read the full guide: docs/AI_POWERED_TEST_HEALING_GUIDE.md")
        logger.info("4. Start using AI healing in your tests!")
    else:
        logger.error("✗ Setup validation failed")
        logger.info("Please check the error messages above and try again")
        sys.exit(1)


if __name__ == "__main__":
    main()
