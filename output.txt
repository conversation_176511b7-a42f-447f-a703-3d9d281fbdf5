2025-08-22 21:15:43,839 - __main__ - WARNING - Dynamic port allocation failed: No module named 'utils.dynamic_port_init'. Using fallback ports.
2025-08-22 21:15:43,853 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 21:15:43,853 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 21:15:43,854 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-22 21:15:43,855 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-22 21:15:43,855 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_android
2025-08-22 21:15:43,856 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-22 21:15:43,857 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-22 21:15:43,857 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android/suites
2025-08-22 21:15:43,858 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_android
2025-08-22 21:15:43,858 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android
2025-08-22 21:15:43,859 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/files_to_push
2025-08-22 21:15:43,859 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-08-22 21:15:43,859 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-08-22 21:15:43,859 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-08-22 21:15:45,696 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/global_values_port_8081.db
2025-08-22 21:15:45,697 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-08-22 21:15:45,699 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 21:15:45,700 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 21:15:45,701 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/test_cases_ios
2025-08-22 21:15:45,701 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios
2025-08-22 21:15:45,702 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-08-22 21:15:45,702 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-22 21:15:45,703 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/test_suites_ios
2025-08-22 21:15:45,703 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-08-22 21:15:45,704 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-08-22 21:15:45,704 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios
2025-08-22 21:15:45,705 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-22 21:15:45,705 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-08-22 21:15:45,706 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-22 21:15:45,708 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-22 21:15:45,709 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-22 21:15:45,764 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-22 21:15:46,322 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 21:15:46,324 - config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-22 21:15:46,324 - config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-22 21:15:46,325 - config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-08-22 21:15:46,325 - config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-22 21:15:46,326 - config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-22 21:15:46,326 - config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-08-22 21:15:46,327 - config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-08-22 21:15:46,327 - config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios
2025-08-22 21:15:46,327 - config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-22 21:15:46,538 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-08-22 21:15:46,539 - utils.database - INFO - Test_steps table schema updated successfully
2025-08-22 21:15:46,539 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-08-22 21:15:46,540 - utils.database - INFO - Screenshots table schema updated successfully
2025-08-22 21:15:46,540 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-08-22 21:15:46,541 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-08-22 21:15:46,542 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-08-22 21:15:46,542 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-08-22 21:15:46,542 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-08-22 21:15:46,542 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-08-22 21:15:46,542 - utils.database - INFO - Database initialized successfully
2025-08-22 21:15:46,542 - utils.database - INFO - Checking initial database state...
2025-08-22 21:15:46,544 - utils.database - INFO - Database state: 0 suites, 0 cases, 0 steps, 0 screenshots, 0 tracking entries
2025-08-22 21:15:46,566 - utils.test_case_manager - INFO - TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/test_cases_ios
2025-08-22 21:15:46,566 - utils.test_case_manager - INFO - TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/test_cases_ios
2025-08-22 21:15:46,567 - app - INFO - Using directories from config_android.py:
2025-08-22 21:15:46,567 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-22 21:15:46,567 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-22 21:15:46,567 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
[2025-08-22 21:15:46,574] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-22 21:15:46,575] INFO in database: Test_steps table schema updated successfully
[2025-08-22 21:15:46,575] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-22 21:15:46,576] INFO in database: Screenshots table schema updated successfully
[2025-08-22 21:15:46,576] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-22 21:15:46,576] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-22 21:15:46,577] INFO in database: action_type column already exists in execution_tracking table
[2025-08-22 21:15:46,577] INFO in database: action_params column already exists in execution_tracking table
[2025-08-22 21:15:46,577] INFO in database: action_id column already exists in execution_tracking table
[2025-08-22 21:15:46,577] INFO in database: Successfully updated execution_tracking table schema
[2025-08-22 21:15:46,577] INFO in database: Database initialized successfully
[2025-08-22 21:15:46,577] INFO in database: Checking initial database state...
[2025-08-22 21:15:46,578] INFO in database: Database state: 1 suites, 0 cases, 2082 steps, 0 screenshots, 2620 tracking entries
[2025-08-22 21:15:46,578] INFO in database: Sample suites: [('5834a8fc-5cf1-4ad1-bafb-bb72f47d1a7f', 'Calc Debug Suite', 'created', '2025-08-13 06:53:10')]
[2025-08-22 21:15:46,581] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-08-22 21:15:46,582] INFO in database: Test_steps table schema updated successfully
[2025-08-22 21:15:46,582] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-22 21:15:46,582] INFO in database: Screenshots table schema updated successfully
[2025-08-22 21:15:46,582] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-22 21:15:46,583] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-22 21:15:46,583] INFO in database: action_type column already exists in execution_tracking table
[2025-08-22 21:15:46,583] INFO in database: action_params column already exists in execution_tracking table
[2025-08-22 21:15:46,584] INFO in database: action_id column already exists in execution_tracking table
[2025-08-22 21:15:46,584] INFO in database: Successfully updated execution_tracking table schema
[2025-08-22 21:15:46,584] INFO in database: Database initialized successfully
[2025-08-22 21:15:46,584] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-08-22 21:15:46,585] INFO in database: step_idx column already exists in execution_tracking table
[2025-08-22 21:15:46,585] INFO in database: action_type column already exists in execution_tracking table
[2025-08-22 21:15:46,585] INFO in database: action_params column already exists in execution_tracking table
[2025-08-22 21:15:46,585] INFO in database: action_id column already exists in execution_tracking table
[2025-08-22 21:15:46,585] INFO in database: Successfully updated execution_tracking table schema
[2025-08-22 21:15:46,585] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-08-22 21:15:46,586] INFO in database: Screenshots table schema updated successfully
[2025-08-22 21:15:46,664] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8300
[2025-08-22 21:15:46,680] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-08-22 21:15:46,680] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-22 21:15:46,682] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-08-22 21:15:46,682] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-22 21:15:46,682] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-08-22 21:15:46,683] INFO in appium_device_controller: Optimized session manager initialized
Starting Mobile App Automation Tool (Android)...
Configuration:
  - Flask server port: 8081
  - Appium server port: 4724
  - WebDriverAgent port: 8300
Open your web browser and navigate to: http://localhost:8081
 * Serving Flask app 'app'
 * Debug mode: on
[2025-08-22 21:15:46,769] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8081
 * Running on http://**************:8081
[2025-08-22 21:15:46,769] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-08-22 21:15:49,679] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-22 21:15:49,757] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET / HTTP/1.1" 200 -
[2025-08-22 21:15:49,828] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,829] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,829] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,831] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /static/js/modules/reportAndFormUtils.js?v=1755864349 HTTP/1.1" 200 -
[2025-08-22 21:15:49,833] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /static/js/modules/actionFormManager.js?v=1755864349 HTTP/1.1" 200 -
[2025-08-22 21:15:49,837] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,837] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /static/js/modules/uiUtils.js?v=1755864349 HTTP/1.1" 200 -
[2025-08-22 21:15:49,837] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,840] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,842] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,846] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/detachable-device-screen.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,847] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,848] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,850] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/css/bulk-locator-manager.css HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,852] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,860] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,861] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/bulk-locator-manager.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,864] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,866] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /static/js/action-manager.js?v=1755864349 HTTP/1.1" 200 -
[2025-08-22 21:15:49,869] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,871] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,875] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,878] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/detachable-device-screen.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,881] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,883] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,890] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,890] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /static/js/action-description.js?v=1755864349 HTTP/1.1" 200 -
[2025-08-22 21:15:49,895] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,898] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,906] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,907] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,911] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,916] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /static/js/main.js?v=1755864349 HTTP/1.1" 200 -
[2025-08-22 21:15:49,921] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,927] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,928] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,929] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,932] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-08-22 21:15:49,957] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-08-22 21:15:49,962] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /api/environments HTTP/1.1" 200 -
[2025-08-22 21:15:49,967] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-08-22 21:15:49,974] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-22 21:15:49,990] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:49] "GET /api/bulk_locator/backups HTTP/1.1" 200 -
[2025-08-22 21:15:50,002] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/test_cases/files HTTP/1.1" 200 -
[2025-08-22 21:15:50,007] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-08-22 21:15:50,017] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/settings HTTP/1.1" 200 -
[2025-08-22 21:15:50,021] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-08-22 21:15:50,029] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-08-22 21:15:50,034] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-22 21:15:50,042] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/environments/current HTTP/1.1" 200 -
[2025-08-22 21:15:50,051] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-08-22 21:15:50,054] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-22 21:15:50,071] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-22 21:15:50,076] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/environments/7/variables HTTP/1.1" 200 -
[2025-08-22 21:15:50,080] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-22 21:15:50,095] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/reference_images HTTP/1.1" 200 -
[2025-08-22 21:15:50,111] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-08-22 21:15:50,146] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-08-22 21:15:50,147] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-08-22 21:15:50,169] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-08-22 21:15:50,189] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:50] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-08-22 21:15:51,233] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:15:51] "GET /api/devices HTTP/1.1" 200 -
[2025-08-22 21:15:53,322] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4724, WDA port: 8300
[2025-08-22 21:15:53,327] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-08-22 21:15:53,327] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-22 21:15:53,331] INFO in appium_device_controller: Appium server is running and ready on port 4724
[2025-08-22 21:15:53,331] INFO in appium_device_controller: Appium server is already running and responsive
[2025-08-22 21:15:53,332] INFO in optimized_session_manager: OptimizedSessionManager initialized
[2025-08-22 21:15:53,332] INFO in appium_device_controller: Optimized session manager initialized
[2025-08-22 21:15:53,332] INFO in appium_device_controller: Connecting to device: PJTCI7EMSSONYPU8 with options: None, platform hint: Android
[2025-08-22 21:15:53,332] DEBUG in appium_device_controller: AppiumDeviceManager integration not available
[2025-08-22 21:15:53,332] INFO in appium_device_controller: AppiumDeviceManager connection failed, falling back to direct connection
[2025-08-22 21:15:53,332] INFO in appium_device_controller: Connection attempt 1/3
[2025-08-22 21:15:53,333] INFO in appium_device_controller: Using provided platform hint: Android
[2025-08-22 21:15:53,333] INFO in appium_device_controller: Added Android-specific UiAutomator2 capabilities with enhanced stability settings
[2025-08-22 21:15:53,333] INFO in appium_device_controller: Desired capabilities: {'platformName': 'Android', 'deviceName': 'PJTCI7EMSSONYPU8', 'udid': 'PJTCI7EMSSONYPU8', 'newCommandTimeout': 600, 'noReset': True, 'automationName': 'UiAutomator2', 'uiautomator2ServerLaunchTimeout': 120000, 'uiautomator2ServerInstallTimeout': 120000, 'adbExecTimeout': 120000, 'skipServerInstallation': False, 'skipDeviceInitialization': False, 'ignoreHiddenApiPolicyError': True, 'disableWindowAnimation': True, 'autoGrantPermissions': True, 'dontStopAppOnReset': True, 'unicodeKeyboard': True, 'resetKeyboard': True, 'skipLogcatCapture': True, 'enforceXPath1': True, 'eventTimings': True, 'printPageSourceOnFindFailure': False, 'shouldTerminateApp': False, 'forceAppLaunch': False, 'systemPort': 8201, 'mjpegServerPort': 7811, 'clearSystemFiles': True, 'skipUnlock': True}
[2025-08-22 21:15:53,334] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'Android', 'appium:deviceName': 'PJTCI7EMSSONYPU8', 'appium:udid': 'PJTCI7EMSSONYPU8', 'appium:newCommandTimeout': 600, 'appium:noReset': True, 'appium:automationName': 'UiAutomator2', 'appium:uiautomator2ServerLaunchTimeout': 120000, 'appium:uiautomator2ServerInstallTimeout': 120000, 'appium:adbExecTimeout': 120000, 'appium:skipServerInstallation': False, 'appium:skipDeviceInitialization': False, 'appium:ignoreHiddenApiPolicyError': True, 'appium:disableWindowAnimation': True, 'appium:autoGrantPermissions': True, 'appium:dontStopAppOnReset': True, 'appium:unicodeKeyboard': True, 'appium:resetKeyboard': True, 'appium:skipLogcatCapture': True, 'appium:enforceXPath1': True, 'appium:eventTimings': True, 'appium:printPageSourceOnFindFailure': False, 'appium:shouldTerminateApp': False, 'appium:forceAppLaunch': False, 'appium:systemPort': 8201, 'appium:mjpegServerPort': 7811, 'appium:clearSystemFiles': True, 'appium:skipUnlock': True}
[2025-08-22 21:15:53,334] INFO in appium_device_controller: Connection attempt 1/3
[2025-08-22 21:15:53,338] WARNING in grid_config: Grid not available, falling back to direct connection for android
[2025-08-22 21:15:53,338] INFO in appium_device_controller: Using connection URL: http://127.0.0.1:4724/wd/hub
[2025-08-22 21:16:04,665] WARNING in appium_device_controller: Healenium services unhealthy, using original driver
[2025-08-22 21:16:04,665] INFO in appium_device_controller: Connection verified with capabilities: Android
[2025-08-22 21:16:04,665] INFO in appium_device_controller: Initializing platform helpers for Android
[2025-08-22 21:16:04,665] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:16:04,680] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:16:04,680] INFO in appium_device_controller: Device dimensions: (1080, 2400)
[2025-08-22 21:16:04,688] INFO in appium_device_controller: Initialized ImageMatcher for Android device: PJTCI7EMSSONYPU8 with Appium driver
[2025-08-22 21:16:04,688] INFO in appium_device_controller: Initializing Android-specific helpers
[2025-08-22 21:16:04,688] INFO in appium_device_controller: Android version: 12.0
[2025-08-22 21:16:04,688] INFO in appium_device_controller: Setting up UiAutomator2 support
[2025-08-22 21:16:04,753] INFO in appium_device_controller: ADB shell access confirmed
[2025-08-22 21:16:04,753] INFO in appium_device_controller: Attempting to initialize UIAutomator2 helper for device: PJTCI7EMSSONYPU8
[2025-08-22 21:16:04,755] DEBUG in appium_device_controller: UIAutomator2Helper class imported successfully
[2025-08-22 21:16:04,854] INFO in uiautomator2_helper: ADB connection confirmed for UIAutomator2 helper (device: PJTCI7EMSSONYPU8)
[2025-08-22 21:16:04,854] INFO in appium_device_controller: UIAutomator2 helper initialized successfully for device: PJTCI7EMSSONYPU8
[2025-08-22 21:16:04,854] INFO in appium_device_controller: UIAutomator2 helper is ready and ADB connection confirmed
[2025-08-22 21:16:04,854] INFO in appium_device_controller: Platform helpers initialization completed
[2025-08-22 21:16:04,854] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-08-22 21:16:04,855] INFO in appium_device_controller: Connection monitoring started
[2025-08-22 21:16:04,855] INFO in action_factory: Registered basic actions: tap, wait
[2025-08-22 21:16:04,857] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-08-22 21:16:04,857] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,858] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-08-22 21:16:04,859] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,859] INFO in action_factory: Registered action handler for 'multiStep'
[2025-08-22 21:16:04,860] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-08-22 21:16:04,860] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,860] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-08-22 21:16:04,861] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,861] INFO in action_factory: Registered action handler for 'swipe'
[2025-08-22 21:16:04,862] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,862] INFO in action_factory: Registered action handler for 'getParam'
[2025-08-22 21:16:04,863] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,863] INFO in action_factory: Registered action handler for 'wait'
[2025-08-22 21:16:04,864] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,864] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-08-22 21:16:04,868] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,868] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-08-22 21:16:04,869] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,869] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-08-22 21:16:04,870] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,870] INFO in action_factory: Registered action handler for 'text'
[2025-08-22 21:16:04,871] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-08-22 21:16:04,871] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,871] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-08-22 21:16:04,873] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,873] INFO in action_factory: Registered action handler for 'waitTill'
[2025-08-22 21:16:04,874] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,874] INFO in action_factory: Registered action handler for 'hookAction'
[2025-08-22 21:16:04,875] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,875] INFO in action_factory: Registered action handler for 'inputText'
[2025-08-22 21:16:04,876] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/global_values_port_8081.db
[2025-08-22 21:16:04,877] INFO in global_values_db: Global values database initialized successfully
[2025-08-22 21:16:04,877] INFO in global_values_db: Using global values from config.py
[2025-08-22 21:16:04,877] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-08-22 21:16:04,879] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,879] INFO in action_factory: Registered action handler for 'setParam'
[2025-08-22 21:16:04,880] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-08-22 21:16:04,880] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,880] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-08-22 21:16:04,882] INFO in action_factory: Special case: Registering android_functions_action.py as 'androidFunctions'
[2025-08-22 21:16:04,882] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,883] INFO in action_factory: Registered action handler for 'androidFunctions'
[2025-08-22 21:16:04,889] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,890] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-08-22 21:16:04,890] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,890] INFO in action_factory: Registered action handler for 'clickImage'
[2025-08-22 21:16:04,891] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,891] INFO in action_factory: Registered action handler for 'tap'
[2025-08-22 21:16:04,892] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,892] INFO in action_factory: Registered action handler for 'clearText'
[2025-08-22 21:16:04,893] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-08-22 21:16:04,893] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,893] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-08-22 21:16:04,894] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-08-22 21:16:04,894] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,894] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-08-22 21:16:04,895] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,895] INFO in action_factory: Registered action handler for 'hideKeyboard'
[2025-08-22 21:16:04,896] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,896] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-08-22 21:16:04,897] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-08-22 21:16:04,897] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,897] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-08-22 21:16:04,898] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,898] INFO in action_factory: Registered action handler for 'launchApp'
[2025-08-22 21:16:04,898] INFO in action_factory: Special case: Registering if_then_steps_action.py as 'ifThenSteps'
[2025-08-22 21:16:04,898] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,899] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-08-22 21:16:04,899] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-08-22 21:16:04,899] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,899] INFO in action_factory: Registered action handler for 'info'
[2025-08-22 21:16:04,900] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,900] INFO in action_factory: Registered action handler for 'waitElement'
[2025-08-22 21:16:04,901] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,901] INFO in action_factory: Registered action handler for 'compareValue'
[2025-08-22 21:16:04,902] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,902] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-08-22 21:16:04,903] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-08-22 21:16:04,903] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,903] INFO in action_factory: Registered action handler for 'exists'
[2025-08-22 21:16:04,904] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,904] INFO in action_factory: Registered action handler for 'clickElement'
[2025-08-22 21:16:04,905] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,905] INFO in action_factory: Registered action handler for 'randomData'
[2025-08-22 21:16:04,906] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,906] INFO in action_factory: Registered action handler for 'getValue'
[2025-08-22 21:16:04,906] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,906] INFO in action_factory: Registered action handler for 'restartApp'
[2025-08-22 21:16:04,907] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-08-22 21:16:04,907] INFO in base_action: Enhanced element finder initialized
[2025-08-22 21:16:04,907] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-08-22 21:16:04,907] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'androidFunctions', 'swipeTillVisible', 'clickImage', 'clearText', 'takeScreenshot', 'tapIfLocatorExists', 'hideKeyboard', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'restartApp', 'doubleTap']
[2025-08-22 21:16:04,907] INFO in action_factory: Handler for 'tap': TapAction
[2025-08-22 21:16:04,907] INFO in action_factory: Handler for 'wait': WaitAction
[2025-08-22 21:16:04,907] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-08-22 21:16:04,907] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'text': TextAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'androidFunctions': AndroidFunctionsAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'clearText': ClearTextAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'hideKeyboard': HideKeyboardAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-08-22 21:16:04,908] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'info': InfoAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-08-22 21:16:04,909] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-08-22 21:16:04,909] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:16:04,909] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:16:05,076] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:16:05,077] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/screenshots/screenshot_20250822_211605.png (save_debug=False)
[2025-08-22 21:16:05,077] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-08-22 21:16:05,078 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-22 21:16:05,078] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
2025-08-22 21:16:06,082 - image_matcher - INFO - Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/debug_images/screenshot_1755861365.png
[2025-08-22 21:16:06,082] INFO in image_matcher: Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/debug_images/screenshot_1755861365.png
[2025-08-22 21:16:06,084] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/screenshots/screenshot_20250822_211605.png
[2025-08-22 21:16:06,084] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211605.png
[2025-08-22 21:16:06,113] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:06] "POST /api/device/connect HTTP/1.1" 200 -
[2025-08-22 21:16:07,124] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:07,124] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-08-22 21:16:07,124 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-22 21:16:07,124] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
2025-08-22 21:16:08,056 - image_matcher - INFO - Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/debug_images/screenshot_1755861367.png
[2025-08-22 21:16:08,056] INFO in image_matcher: Android screenshot saved to: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/debug_images/screenshot_1755861367.png
[2025-08-22 21:16:08,058] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:08,058] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:08,059] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:08] "GET /screenshot?deviceId=PJTCI7EMSSONYPU8&clientSessionId=client_1755861349952_jc3vsc8j8_1755850282684_dd0ke1rlr&t=1755861367120 HTTP/1.1" 200 -
[2025-08-22 21:16:08,081] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:08] "GET /api/test_cases/files HTTP/1.1" 200 -
[2025-08-22 21:16:08,110] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:08] "GET /api/recording/list HTTP/1.1" 200 -
[2025-08-22 21:16:09,310] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:09] "GET /api/test_cases/load/Delivery__CNC_AUANDROID_20250709122628.json HTTP/1.1" 200 -
[2025-08-22 21:16:09,317] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:09] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-22 21:16:10,703] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:10] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-08-22 21:16:10,719] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:10] "POST /api/database/clear_screenshots HTTP/1.1" 200 -
[2025-08-22 21:16:10,726] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:10,726] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
2025-08-22 21:16:10,726 - image_matcher - INFO - Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-22 21:16:10,726] INFO in image_matcher: Taking ADB screenshot for device: PJTCI7EMSSONYPU8
[2025-08-22 21:16:10,744] INFO in _internal: 127.0.0.1 - - [22/Aug/2025 21:16:10] "GET /api/test_cases/load/Onboarding-Start-AU.json HTTP/1.1" 200 -
[2025-08-22 21:16:10,861] INFO in player: Executing action: {'function_name': 'clear_app', 'package_name': 'au.com.kmart', 'timestamp': 1755074292641, 'type': 'androidFunctions'}
[2025-08-22 21:16:10,877] INFO in app: Using directories from config_android.py:
[2025-08-22 21:16:10,877] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/android_data/test_cases
[2025-08-22 21:16:10,877] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/android_data/reference_images
[2025-08-22 21:16:10,877] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
[2025-08-22 21:16:11,681] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:11,681] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:12,287] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/screenshots/screenshot_20250822_211612.png (save_debug=False)
[2025-08-22 21:16:12,287] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:13,319] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios/screenshots/screenshot_20250822_211612.png
[2025-08-22 21:16:13,319] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211612.png
[2025-08-22 21:16:13,346] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:13,347] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:13,509] INFO in appium_device_controller: Launching app: au.com.kmart
[2025-08-22 21:16:13,509] INFO in appium_device_controller: Using device platform 'android' for app au.com.kmart (device platform takes precedence)
[2025-08-22 21:16:13,509] INFO in appium_device_controller: Using UIAutomator2 to launch Android app: au.com.kmart
[2025-08-22 21:16:14,152] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:14,153] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:14,153] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:14,834] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:16:14,834] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211614.png (save_debug=False)
[2025-08-22 21:16:14,834] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:15,522] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211614.png
[2025-08-22 21:16:15,522] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:15,523] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211614.png
[2025-08-22 21:16:15,536] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:15,536] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:15,654] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:16:16,091] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:16,092] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:16,092] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:17,821] INFO in timeout_performance_monitor: Action completed: None - Duration: 2.17s, Efficiency: 0.22, Found: True, Retries: 0
[2025-08-22 21:16:17,822] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:16:19,442] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:16:19,443] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211619.png (save_debug=False)
[2025-08-22 21:16:19,443] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:20,549] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211619.png
[2025-08-22 21:16:20,551] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:20,551] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211619.png
[2025-08-22 21:16:20,565] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:20,565] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:20,691] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:16:20,764] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:16:21,659] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:21,661] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:21,661] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:22,397] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:16:22,398] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211622.png (save_debug=False)
[2025-08-22 21:16:22,398] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:23,134] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211622.png
[2025-08-22 21:16:23,135] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:23,135] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211622.png
[2025-08-22 21:16:23,148] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:23,148] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:23,282] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:16:23,366] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:16:23,890] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:23,891] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:23,892] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:24,470] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:16:24,470] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211624.png (save_debug=False)
[2025-08-22 21:16:24,470] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:25,166] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211624.png
[2025-08-22 21:16:25,169] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:25,169] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211624.png
[2025-08-22 21:16:25,184] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:25,185] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:25,305] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:16:25,827] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:25,828] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:25,828] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:25,986] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:16:27,099] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:16:27,100] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211627.png (save_debug=False)
[2025-08-22 21:16:27,100] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:27,969] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211627.png
[2025-08-22 21:16:27,971] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:27,971] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211627.png
[2025-08-22 21:16:27,984] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:27,984] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:28,117] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:16:28,154] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:16:29,167] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:29,169] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:29,169] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:32,276] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:16:32,277] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211632.png (save_debug=False)
[2025-08-22 21:16:32,277] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:32,882] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211632.png
[2025-08-22 21:16:32,883] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:32,883] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211632.png
[2025-08-22 21:16:32,897] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:32,898] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:33,558] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:33,560] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:33,560] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:34,516] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:16:34,516] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211634.png (save_debug=False)
[2025-08-22 21:16:34,517] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:35,177] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211634.png
[2025-08-22 21:16:35,178] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:35,178] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211634.png
[2025-08-22 21:16:35,191] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:16:35,192] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:16:35,865] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:16:35,866] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:16:35,866] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:02,419] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:02,420] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211702.png (save_debug=False)
[2025-08-22 21:17:02,420] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:03,161] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211702.png
[2025-08-22 21:17:03,163] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:03,163] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211702.png
[2025-08-22 21:17:03,175] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:03,175] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:03,309] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:17:03,520] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:17:03,817] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:03,818] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:03,818] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:04,611] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:04,612] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211704.png (save_debug=False)
[2025-08-22 21:17:04,612] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:05,430] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211704.png
[2025-08-22 21:17:05,431] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:05,431] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211704.png
[2025-08-22 21:17:05,443] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:05,443] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:05,571] INFO in appium_device_controller: Inputting text '<EMAIL>' directly (no locator)
[2025-08-22 21:17:05,571] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:17:06,141] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:06,141] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:06,141] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:07,861] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:17:07,862] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: '<EMAIL>'
[2025-08-22 21:17:09,185] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:09,185] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211709.png (save_debug=False)
[2025-08-22 21:17:09,186] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:10,017] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211709.png
[2025-08-22 21:17:10,018] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:10,018] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211709.png
[2025-08-22 21:17:10,030] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:10,030] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:10,158] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:17:10,355] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:17:10,724] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:10,725] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:10,725] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:13,462] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:13,462] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211713.png (save_debug=False)
[2025-08-22 21:17:13,462] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:14,132] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211713.png
[2025-08-22 21:17:14,133] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:14,133] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211713.png
[2025-08-22 21:17:14,147] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:14,147] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:14,276] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:17:14,682] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:17:14,836] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:14,837] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:14,837] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:15,778] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:15,778] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211715.png (save_debug=False)
[2025-08-22 21:17:15,778] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:16,574] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211715.png
[2025-08-22 21:17:16,575] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:16,575] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211715.png
[2025-08-22 21:17:16,589] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:16,589] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:16,703] INFO in appium_device_controller: Inputting text 'Wonderbaby@6' directly (no locator)
[2025-08-22 21:17:16,703] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:17:17,274] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:17,275] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:17,275] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:18,984] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:17:18,985] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: 'Wonderbaby@6'
[2025-08-22 21:17:20,238] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:20,238] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211720.png (save_debug=False)
[2025-08-22 21:17:20,238] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:20,968] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211720.png
[2025-08-22 21:17:20,969] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:20,969] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211720.png
[2025-08-22 21:17:20,982] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:20,982] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:21,114] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:17:21,315] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:17:21,687] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:21,689] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:21,689] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:22,416] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:22,417] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211722.png (save_debug=False)
[2025-08-22 21:17:22,417] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:23,266] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211722.png
[2025-08-22 21:17:23,267] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:23,267] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211722.png
[2025-08-22 21:17:23,282] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:23,282] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:23,290] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:23,291] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:24,167] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:17:24,167] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:17:24,169] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:17:24,170] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:17:25,186] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:25,186] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:25,934] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:25,934] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:27,094] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:27,095] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211727.png (save_debug=False)
[2025-08-22 21:17:27,095] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:28,004] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211727.png
[2025-08-22 21:17:28,005] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:28,005] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211727.png
[2025-08-22 21:17:28,018] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:28,019] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:28,169] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:28,170] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211728.png (save_debug=False)
[2025-08-22 21:17:28,170] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:29,052] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:29,053] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:29,053] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:29,114] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211728.png
[2025-08-22 21:17:29,115] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:29,115] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211728.png
[2025-08-22 21:17:31,379] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:31,380] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211731.png (save_debug=False)
[2025-08-22 21:17:31,380] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:33,037] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211731.png
[2025-08-22 21:17:33,038] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:33,038] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211731.png
[2025-08-22 21:17:33,051] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:33,051] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:33,183] INFO in appium_device_controller: Inputting text 'Uno Card' directly (no locator)
[2025-08-22 21:17:33,183] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:17:33,936] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:33,937] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:33,937] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:35,490] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:17:35,490] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: 'Uno Card'
[2025-08-22 21:17:36,887] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:36,887] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211736.png (save_debug=False)
[2025-08-22 21:17:36,887] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:37,784] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211736.png
[2025-08-22 21:17:37,785] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:37,785] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211736.png
[2025-08-22 21:17:37,798] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:37,798] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:38,553] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:38,554] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:38,555] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:39,078] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:39,078] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211739.png (save_debug=False)
[2025-08-22 21:17:39,078] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:39,793] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211739.png
[2025-08-22 21:17:39,794] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:39,794] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211739.png
[2025-08-22 21:17:39,808] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:39,809] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:40,533] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:40,534] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:40,534] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:45,573] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:17:45,574] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211745.png (save_debug=False)
[2025-08-22 21:17:45,574] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:46,535] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211745.png
[2025-08-22 21:17:46,537] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:46,537] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211745.png
[2025-08-22 21:17:46,550] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:17:46,551] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:17:46,690] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:17:47,489] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:17:47,490] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:17:47,490] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:01,635] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:18:02,753] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:18:02,753] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211802.png (save_debug=False)
[2025-08-22 21:18:02,754] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:03,703] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211802.png
[2025-08-22 21:18:03,705] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:03,705] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211802.png
[2025-08-22 21:18:03,718] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:18:03,718] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:03,852] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:18:04,197] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:18:04,672] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:04,673] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:04,673] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:05,295] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:18:05,296] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211805.png (save_debug=False)
[2025-08-22 21:18:05,296] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:05,953] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211805.png
[2025-08-22 21:18:05,954] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:05,954] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211805.png
[2025-08-22 21:18:06,002] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:18:06,002] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:06,136] DEBUG in appium_device_controller: Fast element finding: xpath='//android.widget.Button[@content-desc="Checkout"]' (timeout: 5s)
[2025-08-22 21:18:06,289] DEBUG in appium_device_controller: Fast element found: xpath='//android.widget.Button[@content-desc="Checkout"]'
[2025-08-22 21:18:06,289] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.Button[@content-desc="Checkout"]' (timeout=5s, interval=0.5s)
[2025-08-22 21:18:06,289] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.Button[@content-desc="Checkout"]'
[2025-08-22 21:18:06,394] INFO in appium_device_controller: Element found, tapping on it
[2025-08-22 21:18:06,745] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:06,746] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:06,746] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:08,246] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:18:08,247] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211808.png (save_debug=False)
[2025-08-22 21:18:08,247] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:09,145] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211808.png
[2025-08-22 21:18:09,146] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:09,146] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211808.png
[2025-08-22 21:18:09,160] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:18:09,160] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:09,963] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:09,965] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:09,965] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:20,336] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:18:20,337] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211820.png (save_debug=False)
[2025-08-22 21:18:20,337] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:21,111] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211820.png
[2025-08-22 21:18:21,112] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:21,113] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211820.png
[2025-08-22 21:18:21,125] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:18:21,125] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:21,258] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:18:21,282] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:18:21,880] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:21,881] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:21,881] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:22,106] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:18:24,666] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:18:24,667] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211824.png (save_debug=False)
[2025-08-22 21:18:24,667] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:25,323] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211824.png
[2025-08-22 21:18:25,324] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:25,324] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211824.png
[2025-08-22 21:18:25,336] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:18:25,337] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:25,465] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:18:25,465] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:18:25,602] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:18:26,059] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:26,060] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:26,060] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:18:35,807] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:18:35,808] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211835.png (save_debug=False)
[2025-08-22 21:18:35,808] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:36,578] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211835.png
[2025-08-22 21:18:36,579] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:36,579] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211835.png
[2025-08-22 21:18:36,593] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:18:36,594] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:36,731] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:18:36,732] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:18:36,882] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:18:37,270] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:18:37,271] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:18:37,271] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:18:41,539] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:18:41,540] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211841.png (save_debug=False)
[2025-08-22 21:18:41,540] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:18:42,340] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:18:42,340] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:19:00,415] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:00,415] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211841.png
[2025-08-22 21:19:00,427] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:00,427] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:00,573] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:19:01,089] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:19:01,252] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:01,253] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:01,253] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:02,725] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:02,725] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211902.png (save_debug=False)
[2025-08-22 21:19:02,725] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:03,618] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211902.png
[2025-08-22 21:19:03,619] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:03,619] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211902.png
[2025-08-22 21:19:03,631] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:03,631] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:03,773] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:19:03,802] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:19:04,576] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:04,577] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:04,577] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:04,645] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:19:08,381] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:19:10,180] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:10,181] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211910.png (save_debug=False)
[2025-08-22 21:19:10,181] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:11,071] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211910.png
[2025-08-22 21:19:11,072] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:11,072] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211910.png
[2025-08-22 21:19:11,086] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:11,086] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:11,245] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:19:11,913] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:11,914] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:11,914] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:33,330] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:19:35,293] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:35,293] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211935.png (save_debug=False)
[2025-08-22 21:19:35,293] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:36,310] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211935.png
[2025-08-22 21:19:36,311] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:36,311] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211935.png
[2025-08-22 21:19:36,325] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:36,325] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:36,467] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:19:36,617] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:19:37,387] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:37,388] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:37,388] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:38,207] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:38,207] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211938.png (save_debug=False)
[2025-08-22 21:19:38,207] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:39,212] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211938.png
[2025-08-22 21:19:39,214] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:39,214] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211938.png
[2025-08-22 21:19:39,227] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:39,227] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:39,371] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:39,371] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211939.png (save_debug=False)
[2025-08-22 21:19:39,371] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:40,247] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:40,249] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:40,249] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:40,269] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211939.png
[2025-08-22 21:19:40,269] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:40,270] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211939.png
[2025-08-22 21:19:42,573] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:42,573] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211942.png (save_debug=False)
[2025-08-22 21:19:42,573] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:43,694] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211942.png
[2025-08-22 21:19:43,695] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:43,695] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211942.png
[2025-08-22 21:19:43,707] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:43,708] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:43,847] INFO in appium_device_controller: Inputting text 'Uno Card' directly (no locator)
[2025-08-22 21:19:43,847] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:19:44,605] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:44,606] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:44,606] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:46,139] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:19:46,139] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: 'Uno Card'
[2025-08-22 21:19:47,515] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:47,516] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211947.png (save_debug=False)
[2025-08-22 21:19:47,516] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:48,954] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211947.png
[2025-08-22 21:19:48,955] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:48,955] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211947.png
[2025-08-22 21:19:48,968] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:48,968] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:49,828] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:49,829] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:49,829] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:50,233] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:50,234] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211950.png (save_debug=False)
[2025-08-22 21:19:50,234] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:50,897] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211950.png
[2025-08-22 21:19:50,898] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:50,898] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211950.png
[2025-08-22 21:19:50,910] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:50,910] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:51,683] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:51,683] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:51,684] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:56,827] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:19:56,827] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211956.png (save_debug=False)
[2025-08-22 21:19:56,827] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:57,729] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_211956.png
[2025-08-22 21:19:57,730] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:57,730] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_211956.png
[2025-08-22 21:19:57,743] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:19:57,744] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:19:57,889] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:19:58,727] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:19:58,729] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:19:58,729] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:01,444] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:20:02,560] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:20:02,560] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212002.png (save_debug=False)
[2025-08-22 21:20:02,560] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:03,518] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212002.png
[2025-08-22 21:20:03,519] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:03,519] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212002.png
[2025-08-22 21:20:03,532] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:20:03,532] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:03,665] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:20:04,401] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:20:04,472] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:04,477] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:04,477] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:05,515] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:20:05,516] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212005.png (save_debug=False)
[2025-08-22 21:20:05,516] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:06,207] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212005.png
[2025-08-22 21:20:06,208] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:06,208] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212005.png
[2025-08-22 21:20:06,219] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:20:06,219] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:06,344] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:20:06,394] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:20:07,004] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:07,005] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:07,005] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:08,168] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:20:08,169] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212008.png (save_debug=False)
[2025-08-22 21:20:08,169] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:08,992] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212008.png
[2025-08-22 21:20:08,993] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:08,993] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212008.png
[2025-08-22 21:20:09,006] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:20:09,007] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:09,791] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:09,792] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:09,792] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:34,366] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:20:34,366] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212034.png (save_debug=False)
[2025-08-22 21:20:34,367] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:35,165] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212034.png
[2025-08-22 21:20:35,166] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:35,167] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212034.png
[2025-08-22 21:20:35,179] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:20:35,179] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:35,325] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:20:35,528] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:20:35,992] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:35,993] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:35,993] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:36,624] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:20:36,625] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212036.png (save_debug=False)
[2025-08-22 21:20:36,625] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:37,381] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212036.png
[2025-08-22 21:20:37,382] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:37,382] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212036.png
[2025-08-22 21:20:37,395] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:20:37,395] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:37,531] DEBUG in appium_device_controller: Fast element finding: xpath='//*[contains(@text, 'Continue')]' (timeout: 3s)
[2025-08-22 21:20:38,151] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:38,152] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:38,152] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:40,760] DEBUG in appium_device_controller: Fast element not found within 3s: xpath='//*[contains(@text, 'Continue')]'
[2025-08-22 21:20:40,760] DEBUG in appium_device_controller: Fast element finding: accessibility_id='Continue' (timeout: 3s)
[2025-08-22 21:20:48,573] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:20:48,573] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212048.png (save_debug=False)
[2025-08-22 21:20:48,573] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:49,329] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212048.png
[2025-08-22 21:20:49,331] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:49,331] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212048.png
[2025-08-22 21:20:49,344] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:20:49,344] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:20:50,130] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:20:50,131] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:20:50,131] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:01,072] DEBUG in appium_device_controller: Fast element not found within 3s: accessibility_id='Continue'
[2025-08-22 21:21:01,158] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:21:01,170] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:21:01,978] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:21:03,926] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:03,926] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212103.png (save_debug=False)
[2025-08-22 21:21:03,926] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:04,611] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212103.png
[2025-08-22 21:21:04,612] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:04,612] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212103.png
[2025-08-22 21:21:04,626] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:04,626] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:04,748] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:21:04,904] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:21:05,321] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:05,322] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:05,322] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:05,991] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:05,992] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212105.png (save_debug=False)
[2025-08-22 21:21:05,992] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:06,721] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212105.png
[2025-08-22 21:21:06,722] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:06,722] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212105.png
[2025-08-22 21:21:06,734] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:06,734] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:06,866] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:06,867] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212106.png (save_debug=False)
[2025-08-22 21:21:06,867] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:07,347] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:07,347] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:07,348] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:07,420] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:21:07,420] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:21:07,981] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:07,981] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212106.png
[2025-08-22 21:21:10,302] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:10,302] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212110.png (save_debug=False)
[2025-08-22 21:21:10,302] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:11,037] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212110.png
[2025-08-22 21:21:11,038] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:11,038] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212110.png
[2025-08-22 21:21:11,049] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:11,050] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:11,831] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:11,832] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:11,832] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:34,412] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:34,413] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212134.png (save_debug=False)
[2025-08-22 21:21:34,413] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:35,212] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212134.png
[2025-08-22 21:21:35,213] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:35,213] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212134.png
[2025-08-22 21:21:35,225] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:35,225] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:35,365] INFO in appium_device_controller: Inputting text 'FirstName' directly (no locator)
[2025-08-22 21:21:35,365] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:21:36,031] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:36,032] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:36,032] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:37,656] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:21:37,657] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: 'FirstName'
[2025-08-22 21:21:38,899] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:38,899] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212138.png (save_debug=False)
[2025-08-22 21:21:38,899] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:39,656] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212138.png
[2025-08-22 21:21:39,657] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:39,657] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212138.png
[2025-08-22 21:21:39,669] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:39,669] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:39,814] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:21:40,010] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:21:40,406] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:40,407] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:40,407] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:41,119] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:41,120] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212141.png (save_debug=False)
[2025-08-22 21:21:41,120] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:41,794] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212141.png
[2025-08-22 21:21:41,795] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:41,795] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212141.png
[2025-08-22 21:21:41,845] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:41,845] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:42,645] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:42,646] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:42,646] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:43,152] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:43,153] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212143.png (save_debug=False)
[2025-08-22 21:21:43,153] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:43,982] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212143.png
[2025-08-22 21:21:43,983] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:43,983] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212143.png
[2025-08-22 21:21:43,996] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:43,996] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:44,123] INFO in appium_device_controller: Inputting text 'LastName' directly (no locator)
[2025-08-22 21:21:44,123] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:21:44,741] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:44,742] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:44,742] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:46,412] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:21:46,412] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: 'LastName'
[2025-08-22 21:21:47,648] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:21:47,648] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212147.png (save_debug=False)
[2025-08-22 21:21:47,648] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:48,444] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212147.png
[2025-08-22 21:21:48,445] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:48,445] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212147.png
[2025-08-22 21:21:48,459] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:21:48,459] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:21:48,592] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:21:49,220] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:21:49,221] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:21:49,221] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:01,162] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:22:02,280] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:02,281] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212202.png (save_debug=False)
[2025-08-22 21:22:02,281] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:03,053] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212202.png
[2025-08-22 21:22:03,054] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:03,054] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212202.png
[2025-08-22 21:22:03,066] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:03,066] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:03,841] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:03,842] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:03,842] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:04,337] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:04,337] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212204.png (save_debug=False)
[2025-08-22 21:22:04,337] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:05,106] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212204.png
[2025-08-22 21:22:05,106] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:05,107] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212204.png
[2025-08-22 21:22:05,120] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:05,120] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:05,248] INFO in appium_device_controller: Inputting text '<EMAIL>' directly (no locator)
[2025-08-22 21:22:05,248] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:22:05,844] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:05,845] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:05,845] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:07,562] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:22:07,562] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: '<EMAIL>'
[2025-08-22 21:22:08,810] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:08,811] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212208.png (save_debug=False)
[2025-08-22 21:22:08,811] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:09,587] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212208.png
[2025-08-22 21:22:09,588] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:09,588] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212208.png
[2025-08-22 21:22:09,600] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:09,600] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:09,733] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:22:09,925] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:22:10,340] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:10,341] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:10,341] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:11,027] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:11,027] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212211.png (save_debug=False)
[2025-08-22 21:22:11,028] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:11,838] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212211.png
[2025-08-22 21:22:11,839] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:11,839] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212211.png
[2025-08-22 21:22:11,852] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:11,852] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:12,543] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:12,544] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:12,544] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:13,101] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:13,101] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212213.png (save_debug=False)
[2025-08-22 21:22:13,102] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:13,907] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212213.png
[2025-08-22 21:22:13,908] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:13,908] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212213.png
[2025-08-22 21:22:13,921] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:13,921] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:14,050] INFO in appium_device_controller: Inputting text '0400000000' directly (no locator)
[2025-08-22 21:22:14,050] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:22:14,664] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:14,665] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:14,665] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:16,350] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:22:16,350] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: '0400000000'
[2025-08-22 21:22:17,583] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:17,583] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212217.png (save_debug=False)
[2025-08-22 21:22:17,584] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:18,425] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:22:18,425] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:22:19,030] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:19,031] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212217.png
[2025-08-22 21:22:19,043] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:19,043] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:19,821] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:19,821] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:19,822] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:25,215] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:25,215] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212225.png (save_debug=False)
[2025-08-22 21:22:25,216] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:25,946] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212225.png
[2025-08-22 21:22:25,947] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:25,947] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212225.png
[2025-08-22 21:22:25,960] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:25,960] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:26,086] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:22:26,113] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:22:26,701] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:26,702] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:26,702] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:27,047] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:22:28,813] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:28,814] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212228.png (save_debug=False)
[2025-08-22 21:22:28,814] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:29,508] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212228.png
[2025-08-22 21:22:29,509] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:29,509] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212228.png
[2025-08-22 21:22:29,522] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:29,522] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:29,653] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:29,653] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212229.png (save_debug=False)
[2025-08-22 21:22:29,653] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:30,194] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:30,196] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:30,196] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:30,228] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:22:30,228] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:22:30,775] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:30,775] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212229.png
[2025-08-22 21:22:34,085] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:34,085] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212234.png (save_debug=False)
[2025-08-22 21:22:34,085] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:34,708] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212234.png
[2025-08-22 21:22:34,710] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:34,710] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212234.png
[2025-08-22 21:22:34,725] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:34,725] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:35,373] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:35,373] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:35,373] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:45,872] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:22:45,873] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212245.png (save_debug=False)
[2025-08-22 21:22:45,873] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:46,644] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212245.png
[2025-08-22 21:22:46,645] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:46,645] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212245.png
[2025-08-22 21:22:46,659] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:22:46,659] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:22:46,802] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:22:47,444] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:22:47,445] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:22:47,445] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:01,228] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:23:02,342] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:23:02,342] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212302.png (save_debug=False)
[2025-08-22 21:23:02,342] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:03,735] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212302.png
[2025-08-22 21:23:03,737] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:03,737] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212302.png
[2025-08-22 21:23:03,749] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:23:03,749] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:03,889] INFO in appium_device_controller: Inputting text '305 238 Flinders Street' directly (no locator)
[2025-08-22 21:23:03,889] INFO in appium_device_controller: Skipping keyboard management to prevent session termination
[2025-08-22 21:23:04,535] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:04,536] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:04,536] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:06,224] DEBUG in appium_device_controller: Could not hide keyboard (may not be visible): Message: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: The software keyboard cannot be hidden
    at getResponseForW3CError (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/errors.js:1143:9)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileAppAutomation/node_modules/@appium/base-driver/lib/protocol/protocol.js:487:57)
[2025-08-22 21:23:06,224] INFO in appium_device_controller: Using UIAutomator2 helper for direct text input: '305 238 Flinders Street'
[2025-08-22 21:23:07,560] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:23:07,561] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212307.png (save_debug=False)
[2025-08-22 21:23:07,561] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:08,257] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212307.png
[2025-08-22 21:23:08,258] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:08,258] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212307.png
[2025-08-22 21:23:08,270] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:23:08,271] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:08,415] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:23:08,415] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:23:08,579] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:23:09,139] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:09,140] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:09,140] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:23:25,169] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:23:25,169] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212325.png (save_debug=False)
[2025-08-22 21:23:25,169] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:25,818] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212325.png
[2025-08-22 21:23:25,819] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:25,819] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212325.png
[2025-08-22 21:23:25,831] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:23:25,831] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:25,959] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:23:26,012] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:23:26,491] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:26,492] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:26,492] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:27,100] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:23:31,129] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:23:33,227] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:23:33,227] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212333.png (save_debug=False)
[2025-08-22 21:23:33,227] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:33,929] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212333.png
[2025-08-22 21:23:33,930] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:33,931] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212333.png
[2025-08-22 21:23:33,944] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:23:33,944] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:34,075] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:23:34,075] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212334.png (save_debug=False)
[2025-08-22 21:23:34,075] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:23:34,724] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:34,725] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:34,725] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:23:34,779] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212334.png
[2025-08-22 21:23:34,779] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:23:34,779] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212334.png
[2025-08-22 21:24:02,720] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:24:02,721] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212402.png (save_debug=False)
[2025-08-22 21:24:02,721] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:03,519] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212402.png
[2025-08-22 21:24:03,522] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:03,522] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212402.png
[2025-08-22 21:24:03,552] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:24:03,552] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:03,702] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:24:03,702] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212403.png (save_debug=False)
[2025-08-22 21:24:03,702] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:04,345] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:04,347] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:04,347] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:04,379] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212403.png
[2025-08-22 21:24:04,381] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:04,381] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212403.png
[2025-08-22 21:24:06,507] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:24:06,507] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212406.png (save_debug=False)
[2025-08-22 21:24:06,507] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:07,494] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212406.png
[2025-08-22 21:24:07,495] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:07,496] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212406.png
[2025-08-22 21:24:07,509] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:24:07,509] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:07,638] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:24:07,660] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:24:08,271] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:08,273] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:08,273] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:08,286] INFO in appium_device_controller: Swiped from (540,1680) to (540,960)
[2025-08-22 21:24:09,328] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:24:09,329] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212409.png (save_debug=False)
[2025-08-22 21:24:09,329] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:10,146] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212409.png
[2025-08-22 21:24:10,148] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:10,148] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212409.png
[2025-08-22 21:24:10,162] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:24:10,163] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:10,303] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:24:10,303] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:24:10,463] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:24:10,816] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:10,816] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:10,817] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:24:34,112] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:24:34,112] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212434.png (save_debug=False)
[2025-08-22 21:24:34,113] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:35,000] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212434.png
[2025-08-22 21:24:35,002] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:35,002] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212434.png
[2025-08-22 21:24:35,040] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:24:35,040] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:36,010] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:36,011] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:36,011] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:41,212] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:24:41,213] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212441.png (save_debug=False)
[2025-08-22 21:24:41,213] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:41,969] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212441.png
[2025-08-22 21:24:41,970] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:41,970] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212441.png
[2025-08-22 21:24:41,985] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:24:41,985] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:24:42,123] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:24:42,124] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:24:42,280] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:24:42,720] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:24:42,721] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:24:42,721] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:25:01,821] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:25:01,821] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212501.png (save_debug=False)
[2025-08-22 21:25:01,821] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:03,226] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212501.png
[2025-08-22 21:25:03,227] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:03,227] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212501.png
[2025-08-22 21:25:03,242] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:25:03,242] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:03,973] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:25:03,974] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:03,974] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:25:09,422] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:25:09,423] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212509.png (save_debug=False)
[2025-08-22 21:25:09,423] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:10,188] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212509.png
[2025-08-22 21:25:10,189] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:10,189] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212509.png
[2025-08-22 21:25:10,202] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:25:10,202] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:10,334] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:25:10,334] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:25:10,494] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:25:10,944] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:25:10,945] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:10,945] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:25:34,107] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:25:34,108] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212534.png (save_debug=False)
[2025-08-22 21:25:34,108] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:34,722] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212534.png
[2025-08-22 21:25:34,724] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:34,724] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212534.png
[2025-08-22 21:25:34,736] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:25:34,736] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:35,441] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:25:35,442] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:35,442] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:25:40,906] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:25:40,906] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212540.png (save_debug=False)
[2025-08-22 21:25:40,907] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:41,671] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212540.png
[2025-08-22 21:25:41,673] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:41,673] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212540.png
[2025-08-22 21:25:41,687] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:25:41,687] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:25:41,817] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:25:41,817] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:25:42,086] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:25:42,427] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:25:42,429] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:25:42,429] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:26:01,819] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:26:01,820] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212601.png (save_debug=False)
[2025-08-22 21:26:01,820] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:02,526] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212601.png
[2025-08-22 21:26:02,526] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:02,527] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212601.png
[2025-08-22 21:26:02,540] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:26:02,540] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:02,677] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:26:02,677] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:26:02,837] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:26:03,369] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:03,372] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:03,372] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:26:07,907] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:26:07,907] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212607.png (save_debug=False)
[2025-08-22 21:26:07,907] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:08,853] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212607.png
[2025-08-22 21:26:08,854] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:08,854] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212607.png
[2025-08-22 21:26:08,867] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:26:08,867] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:09,622] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:09,623] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:09,623] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:15,040] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:26:15,041] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212615.png (save_debug=False)
[2025-08-22 21:26:15,041] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:15,830] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212615.png
[2025-08-22 21:26:15,831] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:15,831] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212615.png
[2025-08-22 21:26:15,844] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:26:15,844] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:15,976] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:26:15,976] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:26:16,130] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:26:16,677] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:16,678] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:16,678] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:26:34,132] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:26:34,133] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212634.png (save_debug=False)
[2025-08-22 21:26:34,133] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:34,798] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212634.png
[2025-08-22 21:26:34,799] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:34,799] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212634.png
[2025-08-22 21:26:34,813] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:26:34,813] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:35,573] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:35,574] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:35,574] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:40,983] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:26:40,983] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212640.png (save_debug=False)
[2025-08-22 21:26:40,983] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:41,757] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212640.png
[2025-08-22 21:26:41,758] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:41,758] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212640.png
[2025-08-22 21:26:41,771] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:26:41,771] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:41,913] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:26:41,913] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212641.png (save_debug=False)
[2025-08-22 21:26:41,913] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:26:42,451] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:42,452] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:26:42,452] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:26:42,550] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:26:42,551] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:27:00,562] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:00,563] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212641.png
[2025-08-22 21:27:02,899] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:27:02,900] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212702.png (save_debug=False)
[2025-08-22 21:27:02,900] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:03,560] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212702.png
[2025-08-22 21:27:03,561] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:03,561] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212702.png
[2025-08-22 21:27:03,575] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:27:03,575] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:03,704] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:27:03,704] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:27:03,871] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:27:04,217] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:04,218] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:04,218] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:27:08,625] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:27:08,625] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212708.png (save_debug=False)
[2025-08-22 21:27:08,626] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:09,343] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212708.png
[2025-08-22 21:27:09,344] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:09,344] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212708.png
[2025-08-22 21:27:09,356] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:27:09,356] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:10,096] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:10,097] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:10,097] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:15,527] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:27:15,527] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212715.png (save_debug=False)
[2025-08-22 21:27:15,527] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:16,286] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212715.png
[2025-08-22 21:27:16,287] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:16,287] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212715.png
[2025-08-22 21:27:16,300] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:27:16,300] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:16,439] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:27:16,439] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:27:16,605] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:27:17,068] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:17,070] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:17,070] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:27:34,126] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:27:34,126] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212734.png (save_debug=False)
[2025-08-22 21:27:34,127] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:35,104] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212734.png
[2025-08-22 21:27:35,105] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:35,105] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212734.png
[2025-08-22 21:27:35,119] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:27:35,119] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:35,883] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:35,883] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:35,884] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:41,292] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:27:41,293] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212741.png (save_debug=False)
[2025-08-22 21:27:41,293] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:42,053] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212741.png
[2025-08-22 21:27:42,054] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:42,054] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212741.png
[2025-08-22 21:27:42,068] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:27:42,068] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:42,204] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:27:42,204] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212742.png (save_debug=False)
[2025-08-22 21:27:42,204] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:27:42,917] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212742.png
[2025-08-22 21:27:42,917] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:42,918] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:42,918] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:27:42,918] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:27:42,919] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212742.png
[2025-08-22 21:28:02,676] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:28:02,676] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212802.png (save_debug=False)
[2025-08-22 21:28:02,676] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:03,339] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212802.png
[2025-08-22 21:28:03,341] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:03,341] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212802.png
[2025-08-22 21:28:03,355] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:28:03,355] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:04,114] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:28:04,115] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:04,116] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:28:09,516] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:28:09,516] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212809.png (save_debug=False)
[2025-08-22 21:28:09,516] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:10,429] WARNING in appium_device_controller: ImageMatcher screenshot failed, falling back to native Appium
[2025-08-22 21:28:10,429] INFO in appium_device_controller: Taking screenshot using native Appium driver (attempt 1/3)
[2025-08-22 21:28:33,584] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:33,584] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212809.png
[2025-08-22 21:28:33,597] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:28:33,598] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:33,737] INFO in appium_device_controller: Initializing Airtest connection for device: PJTCI7EMSSONYPU8...
[2025-08-22 21:28:33,737] INFO in appium_device_controller: Connecting to Android device with Airtest using URI: android://127.0.0.1:5037/PJTCI7EMSSONYPU8?cap_method=JAVACAP&ori_method=ADBORI
[2025-08-22 21:28:33,898] ERROR in appium_device_controller: Failed to connect to Android device with Airtest: device idx not found in: ['PJTCI7EMSSONYPU8'] or [0]
[2025-08-22 21:28:34,452] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:28:34,453] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:34,453] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/venv/lib/python3.13/site-packages/airtest/core/android/cap_methods/adbcap.py:10: UserWarning: Currently using ADB screenshots, the efficiency may be very low.
  warnings.warn("Currently using ADB screenshots, the efficiency may be very low.")
[2025-08-22 21:28:39,209] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:28:39,210] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212839.png (save_debug=False)
[2025-08-22 21:28:39,210] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:39,913] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212839.png
[2025-08-22 21:28:39,914] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:39,914] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212839.png
[2025-08-22 21:28:39,926] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:28:39,926] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:40,673] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:28:40,674] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:40,674] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:28:46,095] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:28:46,095] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212846.png (save_debug=False)
[2025-08-22 21:28:46,095] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:47,100] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212846.png
[2025-08-22 21:28:47,101] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:47,101] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212846.png
[2025-08-22 21:28:47,115] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:28:47,115] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:28:47,259] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:28:48,032] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:28:48,033] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:28:48,033] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:01,072] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:29:02,177] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:29:02,178] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212902.png (save_debug=False)
[2025-08-22 21:29:02,178] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:03,006] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212902.png
[2025-08-22 21:29:03,007] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:03,007] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212902.png
[2025-08-22 21:29:03,020] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:29:03,020] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:03,147] DEBUG in appium_device_controller: Fast element finding: xpath='//android.widget.Button[@content-desc="Checkout"]' (timeout: 5s)
[2025-08-22 21:29:03,241] DEBUG in appium_device_controller: Fast element found: xpath='//android.widget.Button[@content-desc="Checkout"]'
[2025-08-22 21:29:03,242] INFO in appium_device_controller: Tapping on element with xpath='//android.widget.Button[@content-desc="Checkout"]' (timeout=5s, interval=0.5s)
[2025-08-22 21:29:03,242] INFO in appium_device_controller: Waiting for element to be clickable: xpath='//android.widget.Button[@content-desc="Checkout"]'
[2025-08-22 21:29:03,350] INFO in appium_device_controller: Element found, tapping on it
[2025-08-22 21:29:03,941] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:03,942] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:03,942] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:05,194] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:29:05,195] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212905.png (save_debug=False)
[2025-08-22 21:29:05,195] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:05,998] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212905.png
[2025-08-22 21:29:06,000] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:06,000] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212905.png
[2025-08-22 21:29:06,014] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:29:06,014] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:06,828] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:06,830] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:06,830] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:35,270] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:29:35,270] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212935.png (save_debug=False)
[2025-08-22 21:29:35,270] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:36,105] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212935.png
[2025-08-22 21:29:36,106] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:36,106] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212935.png
[2025-08-22 21:29:36,120] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:29:36,121] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:36,272] INFO in appium_device_controller: Getting device dimensions with enhanced accuracy
[2025-08-22 21:29:36,326] INFO in appium_device_controller: Got device dimensions from Appium: 1080x2400
[2025-08-22 21:29:37,034] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:37,035] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:37,035] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:38,146] INFO in appium_device_controller: Swiped from (540,1680) to (540,720)
[2025-08-22 21:29:40,068] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:29:40,068] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212940.png (save_debug=False)
[2025-08-22 21:29:40,068] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:41,015] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212940.png
[2025-08-22 21:29:41,016] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:41,016] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212940.png
[2025-08-22 21:29:41,031] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:29:41,031] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:41,166] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:29:41,424] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:29:41,790] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:41,791] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:41,791] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:42,557] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:29:42,557] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212942.png (save_debug=False)
[2025-08-22 21:29:42,557] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:43,312] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212942.png
[2025-08-22 21:29:43,313] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:43,313] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212942.png
[2025-08-22 21:29:43,325] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:29:43,326] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:43,457] INFO in appium_device_controller: Health checks suspended during session refresh
[2025-08-22 21:29:44,015] INFO in appium_device_controller: Health checks resumed after session refresh
[2025-08-22 21:29:44,143] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:44,146] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:44,146] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:45,147] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots
[2025-08-22 21:29:45,147] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212945.png (save_debug=False)
[2025-08-22 21:29:45,147] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:46,094] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/screenshot_20250822_212945.png
[2025-08-22 21:29:46,095] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:46,095] INFO in appium_device_controller: Generated screenshot URL: /screenshots/screenshot_20250822_212945.png
[2025-08-22 21:29:46,109] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png (save_debug=False)
[2025-08-22 21:29:46,109] INFO in appium_device_controller: Taking screenshot using ImageMatcher (attempt 1/3)
[2025-08-22 21:29:47,087] INFO in appium_device_controller: Screenshot taken with ImageMatcher and copied to /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app_android/static/screenshots/device_PJTCI7EMSSONYPU8_latest.png
[2025-08-22 21:29:47,088] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250822_211613/screenshots/latest.png
[2025-08-22 21:29:47,089] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_PJTCI7EMSSONYPU8_latest.png
